<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Magento\Theme\Block\Html\Topmenu" type="Diamond\Theme\Block\Html\Topmenu" />

    <!-- Diamond Aircraft Custom Icons ViewModel -->
    <type name="Diamond\Theme\ViewModel\DiamondIcons">
        <arguments>
            <argument name="iconPathPrefix" xsi:type="string">Hyva_Theme::svg/diamond</argument>
        </arguments>
    </type>

    <!-- Diamond Aircraft Safe Product Attributes ViewModel -->
    <type name="Diamond\Theme\ViewModel\SafeProductAttributes">
        <arguments>
            <argument name="hyvaProductAttributes" xsi:type="object">Hyva\Theme\ViewModel\ProductAttributes</argument>
        </arguments>
    </type>

    <!-- Diamond Aircraft Safe Attribute ViewModel -->
    <type name="Diamond\Theme\ViewModel\SafeAttribute">
        <arguments>
            <argument name="productResource" xsi:type="object">Magento\Catalog\Model\ResourceModel\Product</argument>
            <argument name="safeProductAttributes" xsi:type="object">Diamond\Theme\ViewModel\SafeProductAttributes</argument>
            <argument name="searchCriteriaBuilder" xsi:type="object">Magento\Framework\Api\SearchCriteriaBuilder</argument>
            <argument name="attributeGroupRepository" xsi:type="object">Magento\Catalog\Api\ProductAttributeGroupRepositoryInterface</argument>
            <argument name="attributeRepository" xsi:type="object">Magento\Catalog\Api\ProductAttributeRepositoryInterface</argument>
            <argument name="sortOrderBuilder" xsi:type="object">Magento\Framework\Api\SortOrderBuilder</argument>
        </arguments>
    </type>
</config>
