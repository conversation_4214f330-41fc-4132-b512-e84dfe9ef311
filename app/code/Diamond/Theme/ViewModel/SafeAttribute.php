<?php
/**
 * Diamond Aircraft Safe Attribute ViewModel
 * Replaces CopeX Attribute ViewModel with safe array handling
 */

declare(strict_types=1);

namespace Diamond\Theme\ViewModel;

use Diamond\Theme\ViewModel\SafeProductAttributes;
use Magento\Catalog\Api\ProductAttributeGroupRepositoryInterface;
use Magento\Catalog\Api\ProductAttributeRepositoryInterface;
use Magento\Catalog\Model\ResourceModel\Product as ProductResource;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Api\SortOrderBuilder;
use Magento\Framework\View\Element\Block\ArgumentInterface;

class SafeAttribute implements ArgumentInterface
{
    private SafeProductAttributes $safeProductAttributes;
    private SearchCriteriaBuilder $searchCriteriaBuilder;
    private ProductAttributeGroupRepositoryInterface $attributeGroupRepository;
    private ProductAttributeRepositoryInterface $attributeRepository;
    private SortOrderBuilder $sortOrderBuilder;
    private ProductResource $productResource;

    public function __construct(
        ProductResource $productResource,
        SafeProductAttributes $safeProductAttributes,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        ProductAttributeGroupRepositoryInterface $attributeGroupRepository,
        ProductAttributeRepositoryInterface $attributeRepository,
        SortOrderBuilder $sortOrderBuilder
    ) {
        $this->safeProductAttributes = $safeProductAttributes;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->attributeRepository = $attributeRepository;
        $this->sortOrderBuilder = $sortOrderBuilder;
        $this->productResource = $productResource;
        $this->attributeGroupRepository = $attributeGroupRepository;
    }

    public function getAttributesByGroup($groupName)
    {
        $groupAttributes = [];
        $sortOrder = $this->sortOrderBuilder
            ->setField('sort_order')
            ->setAscendingDirection()
            ->create();
        $group = current($this->attributeGroupRepository->getList($searchCriteria = $this->searchCriteriaBuilder
            ->addFilter(\Magento\Eav\Api\Data\AttributeGroupInterface::GROUP_NAME, $groupName)->create())->getItems() ?? []);
        if ($group) {
            $searchCriteria = $this->searchCriteriaBuilder
                ->addFilter(\Magento\Eav\Api\Data\AttributeGroupInterface::GROUP_ID, $group->getId())
                ->addFilter(\Magento\Catalog\Api\Data\ProductAttributeInterface::IS_VISIBLE, 1) //if you want only visible attributes
                ->addSortOrder($sortOrder)
                ->create();
            $groupAttributes = $this->attributeRepository->getList($searchCriteria)->getItems();
        }
        return $groupAttributes;
    }

    public function getAttributeValue($attribute, $product)
    {
        $productAttributeData = $this->getAttributeData($attribute, $product);
        return $productAttributeData['value'];
    }

    public function getAttributeStoreLabel($attribute,$product){
        $productAttributeData = $this->getAttributeData($attribute, $product);
        return $productAttributeData['label'];
    }

    public function getAttributeData($attribute, $product){
        $attribute = $this->getAttribute($attribute);
        if ($attribute) {
            return $this->safeProductAttributes->getAttributeData($attribute, $product);
        }
        return [];
    }

    public function getAttribute($attribute)
    {
        if($attribute instanceof \Magento\Catalog\Model\ResourceModel\Eav\Attribute){
            return $attribute;
        }
        if(is_string($attribute)){
            return $this->productResource->getAttribute($attribute);
        }
        return null;
    }
}
