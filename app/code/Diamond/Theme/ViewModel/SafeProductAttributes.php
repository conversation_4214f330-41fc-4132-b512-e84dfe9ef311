<?php
/**
 * Diamond Aircraft Safe Product Attributes ViewModel
 * Fixes array to string conversion errors in Hyvä ProductAttributes
 */

declare(strict_types=1);

namespace Diamond\Theme\ViewModel;

use Hyva\Theme\ViewModel\ProductAttributes;
use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Catalog\Model\ResourceModel\Eav\Attribute;
use Magento\Framework\View\Element\Block\ArgumentInterface;

class SafeProductAttributes implements ArgumentInterface
{
    private ProductAttributes $hyvaProductAttributes;

    public function __construct(
        ProductAttributes $hyvaProductAttributes
    ) {
        $this->hyvaProductAttributes = $hyvaProductAttributes;
    }

    /**
     * Get attribute data with safe array handling
     *
     * @param Attribute $attribute
     * @param ProductInterface $product
     * @return array
     */
    public function getAttributeData(Attribute $attribute, ProductInterface $product): array
    {
        try {
            // Try to get the attribute data from Hyvä
            $attributeData = $this->hyvaProductAttributes->getAttributeData($attribute, $product);
            
            // Ensure the value is properly handled if it's an array
            if (isset($attributeData['value']) && is_array($attributeData['value'])) {
                $attributeData['value'] = $this->convertArrayToString($attributeData['value']);
            }
            
            return $attributeData;
        } catch (\Throwable $e) {
            // If there's an error, fall back to safe attribute handling
            return $this->getSafeAttributeData($attribute, $product);
        }
    }

    /**
     * Safe fallback method to get attribute data
     *
     * @param Attribute $attribute
     * @param ProductInterface $product
     * @return array
     */
    private function getSafeAttributeData(Attribute $attribute, ProductInterface $product): array
    {
        $attributeCode = $attribute->getAttributeCode();
        $value = $product->getData($attributeCode);
        
        // Handle array values
        if (is_array($value)) {
            $value = $this->convertArrayToString($value);
        }
        
        // Handle null values
        if ($value === null) {
            $value = '';
        }
        
        return [
            'label' => $attribute->getStoreLabel() ?: $attribute->getFrontendLabel(),
            'value' => (string) $value,
            'code' => $attributeCode
        ];
    }

    /**
     * Convert array to string safely
     *
     * @param array $value
     * @return string
     */
    private function convertArrayToString(array $value): string
    {
        // Filter out empty values
        $filteredValues = array_filter($value, function($item) {
            return !empty($item) && $item !== '';
        });
        
        if (empty($filteredValues)) {
            return '';
        }
        
        // If it's a simple array, join with commas
        if (array_keys($filteredValues) === range(0, count($filteredValues) - 1)) {
            return implode(', ', $filteredValues);
        }
        
        // If it's an associative array, format as key: value pairs
        $formatted = [];
        foreach ($filteredValues as $key => $val) {
            if (is_numeric($key)) {
                $formatted[] = (string) $val;
            } else {
                $formatted[] = $key . ': ' . $val;
            }
        }
        
        return implode(', ', $formatted);
    }
}
