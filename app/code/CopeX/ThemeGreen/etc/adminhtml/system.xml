<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <tab id="copex" translate="label" sortOrder="110">
            <label>CopeX</label>
        </tab>
        <section id="theme_green" translate="label" sortOrder="300" showInDefault="1" showInWebsite="1" showInStore="1">
            <label>Theme</label>
            <tab>copex</tab>
            <resource>CopeX_ThemeGreen::config</resource>
            <group id="product_detail_page" translate="label" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Product Detail Page</label>
                <field id="datasheet" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Datasheet</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <config_path>theme_green/product_detail_page/datasheet</config_path>
                </field>
            </group>
        </section>
    </system>
</config>
