<?php
/**
 * Diamond Aircraft Desktop Navigation Template
 * Based on Hyvä Themes - https://hyva.io
 */

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Hyva\Theme\ViewModel\Navigation;
use Magento\Framework\View\Element\Template;
use Magento\Framework\Escaper;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var Navigation $viewModelNavigation */
$viewModelNavigation = $viewModels->require(Navigation::class, $block);

/** @var HeroiconsSolid $heroiconsSolid */
$heroiconsSolid = $viewModels->require(HeroiconsSolid::class);

$uniqueId = '_' . uniqid();

// Order is important here: 1. build the menu data, 2. then set the cache tags from the view model identities
$menuItems = $viewModelNavigation->getNavigation(4);
$block->setData('cache_tags', $viewModelNavigation->getIdentities());

// Check if this is the homepage for transparent header
$isHomepage = $block->getRequest()->getFullActionName() === 'cms_index_index';
?>
<div x-data="initMenuDesktop<?= $escaper->escapeHtml($uniqueId) ?>()"
     class="z-20 order-2 sm:order-1 lg:order-2 navigation hidden lg:flex"
>
    <!-- Desktop Navigation -->
    <div x-ref="nav-desktop"
         @load.window="setActiveMenu($root)"
         class="hidden lg:block">
        <nav
            class="relative"
            aria-label="<?= $escaper->escapeHtmlAttr(__('Main menu')) ?>"
        >
            <ul class="flex justify-center items-center space-x-8">
                <?php foreach ($menuItems as $index => $menuItem): ?>
                    <li class="relative level-0 group"
                        @mouseenter="showSubmenu('<?= /* @noEscape */ (string) $index ?>')"
                        @mouseleave="hideSubmenuWithDelay('<?= /* @noEscape */ (string) $index ?>')"
                        @keyup.escape="hoverPanelActiveId = 0"
                    >
                        <div class="flex items-center">
                            <a class="<?= $isHomepage ? 'text-white hover:text-blue-200' : 'text-gray-800 hover:text-diamond-blue' ?> font-medium py-4 px-2 transition-colors duration-200 border-b-2 border-transparent hover:border-diamond-blue group-hover:border-diamond-blue"
                               href="<?= $escaper->escapeUrl($menuItem['url']) ?>"
                               title="<?= $escaper->escapeHtmlAttr($menuItem['name']) ?>"
                               @focus="hoverPanelActiveId = 0"
                            >
                                <?= $escaper->escapeHtml($menuItem['name']) ?>
                            </a>
                            <?php if (!empty($menuItem['childData'])): ?>
                                <button
                                    type="button"
                                    data-sr-button-id="<?= $escaper->escapeHtmlAttr($index) ?>"
                                    :aria-expanded="hoverPanelActiveId === '<?= /* @noEscape */ (string) $index ?>' ? 'true' : 'false'"
                                    @click="openMenuOnClick('<?= /* @noEscape */ (string) $index ?>')"
                                    class="<?= $isHomepage ? 'text-white hover:text-blue-200' : 'text-gray-600 hover:text-diamond-blue' ?> rounded-full p-1 ml-1 transition-colors duration-200"
                                >
                                    <?= $heroiconsSolid->chevronDownHtml("w-4 h-4 transition-transform duration-200 group-hover:rotate-180", 16, 16, ['aria-hidden' => 'true']) ?>
                                    <span class="sr-only">
                                        <?= $escaper->escapeHtml(__('Show submenu for %1 category', $menuItem['name'])) ?>
                                    </span>
                                </button>
                            <?php endif; ?>
                        </div>

                        <?php if (!empty($menuItem['childData'])): ?>
                            <ul
                                class="absolute top-full left-1/2 transform -translate-x-1/2 z-50 hidden bg-white shadow-xl rounded-lg py-4 px-2 w-max min-w-48 border border-gray-100"
                                :class="{
                                    'hidden' : hoverPanelActiveId !== '<?= /* @noEscape */ (string) $index ?>',
                                    'block' : hoverPanelActiveId === '<?= /* @noEscape */ (string) $index ?>'
                                }"
                                style="animation: fadeInUp 0.2s ease-out;"
                                @mouseenter="showSubmenu('<?= /* @noEscape */ (string) $index ?>')"
                                @mouseleave="hideSubmenuWithDelay('<?= /* @noEscape */ (string) $index ?>')"
                            >
                                <!-- View All Link -->
                                <li class="border-b border-gray-100 pb-2 mb-2">
                                    <a href="<?= $escaper->escapeUrl($menuItem['url']) ?>"
                                       title="<?= $escaper->escapeHtmlAttr($menuItem['name']) ?>"
                                       class="block px-4 py-2 text-diamond-blue font-semibold hover:bg-gray-50 rounded-md transition-colors duration-200"
                                       @keyup.escape="$nextTick(() => document.querySelector('[data-sr-button-id=<?= $escaper->escapeJs($index) ?>]').focus())"
                                    >
                                        <?= $escaper->escapeHtml(__('View All %1', $menuItem['name'])) ?>
                                    </a>
                                </li>

                                <?php foreach ($menuItem['childData'] as $subMenuItem): ?>
                                    <li>
                                        <a href="<?= $escaper->escapeUrl($subMenuItem['url']) ?>"
                                           title="<?= $escaper->escapeHtmlAttr($subMenuItem['name']) ?>"
                                           class="block px-4 py-2 text-gray-700 hover:text-diamond-blue hover:bg-gray-50 rounded-md transition-colors duration-200"
                                           @keyup.escape="$nextTick(() => document.querySelector('[data-sr-button-id=<?= $escaper->escapeJs($index) ?>]').focus())"
                                        >
                                            <?= $escaper->escapeHtml($subMenuItem['name']) ?>
                                        </a>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        <?php endif; ?>
                    </li>
                <?php endforeach; ?>
            </ul>
        </nav>
    </div>
</div>

<script>
    'use strict';

    const initMenuDesktop<?= $escaper->escapeHtml($uniqueId) ?> = () => {
        return {
            hoverPanelActiveId: 0,
            hideTimeout: null,
            setActiveMenu(menuNode) {
                Array.from(menuNode.querySelectorAll('a')).filter(link => {
                    return link.href === window.location.href.split('?')[0];
                }).map(item => {
                    item.classList.add('text-primary', 'font-bold', 'border-primary');
                    item.closest('li.level-0') &&
                    item.closest('li.level-0').classList.add('border-primary');
                });
            },
            openMenuOnClick(index) {
                this.hoverPanelActiveId = this.hoverPanelActiveId === index ? 0 : index;
            },
            showSubmenu(index) {
                // Clear any pending hide timeout
                if (this.hideTimeout) {
                    clearTimeout(this.hideTimeout);
                    this.hideTimeout = null;
                }
                this.hoverPanelActiveId = index;
            },
            hideSubmenuWithDelay(index) {
                // Add a small delay before hiding to allow mouse movement to submenu
                this.hideTimeout = setTimeout(() => {
                    if (this.hoverPanelActiveId === index) {
                        this.hoverPanelActiveId = 0;
                    }
                    this.hideTimeout = null;
                }, 150); // 150ms delay
            }
        }
    }
</script>

<style>
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}
</style>
