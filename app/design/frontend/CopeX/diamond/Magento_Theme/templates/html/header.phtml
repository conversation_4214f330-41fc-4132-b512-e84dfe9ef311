<?php
/**
 * Diamond Aircraft Hyvä Theme Header
 * Based on Hyvä Themes - https://hyva.io
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\StoreConfig;
use Magento\Checkout\Block\Cart\Sidebar as SidebarCart;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Escaper $escaper */
/** @var Template $block */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

/** @var StoreConfig $storeConfig */
$storeConfig = $viewModels->require(StoreConfig::class);
$showMiniCart = $storeConfig->getStoreConfig(SidebarCart::XML_PATH_CHECKOUT_SIDEBAR_DISPLAY);

// Check if this is the homepage for transparent header
$isHomepage = $block->getRequest()->getFullActionName() === 'cms_index_index';
?>
<script>
    function initHeader () {
        return {
            searchOpen: false,
            cart: {},
            isCartOpen: false,
            getData(data) {
                if (data.cart) { this.cart = data.cart }
            },
            isCartEmpty() {
                return !this.cart.summary_count
            },
            toggleCart(event) {
                if (event.detail && event.detail.isOpen !== undefined) {
                    this.isCartOpen = event.detail.isOpen
                    if (!this.isCartOpen && this.$refs && this.$refs.cartButton) {
                        this.$refs.cartButton.focus()
                    }
                } else {
                    this.isCartOpen = true
                }
            }
        }
    }
    function initCompareHeader() {
        return {
            compareProducts: null,
            itemCount: 0,
            receiveCompareData(data) {
                if (data['compare-products']) {
                    this.compareProducts = data['compare-products'];
                    this.itemCount = this.compareProducts.count;
                }
            }
        }
    }
</script>

<!-- Diamond Aircraft Header -->
<div id="header"
     class="<?= $isHomepage ? 'absolute top-0 left-0 right-0 z-50 bg-transparent' : 'relative z-30 bg-white shadow-sm border-b border-gray-200' ?> w-full "
     x-data="initHeader()"
     @private-content-loaded.window="getData(event.detail.data)"
>
    <!-- Info Banner (only on homepage) -->
    <?php if ($isHomepage): ?>
    <div class="bg-diamond-blue text-white text-center py-2 px-4 text-sm">
        <span><?= $escaper->escapeHtml(__('Free shipping on orders over $100 | Diamond Aircraft Official Store')) ?></span>
    </div>
    <?php endif; ?>

    <!-- Main Header Container -->
    <div class="max-w-screen-2xl mx-auto px-4 py-3">
        <div class="flex flex-wrap lg:flex-nowrap items-center justify-between w-full">

            <!-- Logo -->
            <div class="order-1 sm:order-2 lg:order-1 w-full pb-2 sm:w-auto sm:pb-0">
                <?= $block->getChildHtml('logo'); ?>
            </div>

            <!-- Main Navigation (Desktop) -->
            <div class="hidden lg:flex order-2 flex-1 justify-center">
                <?= $block->getChildHtml('topmenu') ?>
            </div>

            <!-- Header Meta (Right Side) -->
            <div class="flex items-center gap-4 order-3 md:-mr-1">

                <!-- Language Switcher -->
                <div class="hidden md:flex items-center">
                    <?= $block->getChildHtml('store_language') ?>
                </div>

                <!-- Compare Icon -->
                <a id="compare-link"
                   class="relative inline-block rounded-full p-2 <?= $isHomepage ? 'text-white hover:bg-white hover:bg-opacity-20' : 'text-gray-700 hover:bg-gray-100' ?> transition-colors duration-200 invisible"
                   :class="{ 'invisible': !(itemCount > 0) }"
                   href="<?= $escaper->escapeUrl($block->getUrl('catalog/product_compare/index')) ?>"
                   title="<?= $escaper->escapeHtmlAttr(__('Compare Products')) ?>"
                   x-data="initCompareHeader()"
                   @private-content-loaded.window="receiveCompareData($event.detail.data)"
                   :aria-label="`
                        <?= $escaper->escapeHtmlAttr(__('Compare Products')) ?>,
                        ${itemCount > 1
                            ? hyva.str('<?= $escaper->escapeJs(__('%1 Items')) ?>', itemCount)
                            : hyva.str('<?= $escaper->escapeJs(__('%1 Item')) ?>', itemCount)
                        }`"
                >
                    <?= $heroicons->scaleHtml("w-5 h-5", 20, 20, ["aria-hidden" => "true"]) ?>
                    <span
                        x-text="itemCount"
                        class="absolute -top-1 -right-1 h-4 px-1.5 py-0.5 rounded-full bg-secondary text-white text-xs font-semibold leading-none text-center"
                        aria-hidden="true"
                    ></span>
                </a>

                <!-- Search Icon -->
                <button
                    id="menu-search-icon"
                    class="rounded-full p-2 <?= $isHomepage ? 'text-white hover:bg-white hover:bg-opacity-20' : 'text-gray-700 hover:bg-gray-100' ?> transition-colors duration-200"
                    @click.prevent="
                        searchOpen = !searchOpen;
                        $dispatch('search-open');
                    "
                    aria-label="<?= $escaper->escapeHtmlAttr(__('Toggle search form')) ?>"
                    aria-haspopup="true"
                    :aria-expanded="searchOpen"
                    x-ref="searchButton"
                >
                    <?= $heroicons->searchHtml("w-5 h-5", 20, 20, ["aria-hidden" => "true"]) ?>
                </button>

                <!-- Additional Header Elements -->
                <?= $block->getChildHtml('additional') ?>

                <!-- Customer Icon & Dropdown -->
                <div class="<?= $isHomepage ? 'text-white' : 'text-gray-700' ?>">
                    <?= $block->getChildHtml('customer') ?>
                </div>

                <!-- Cart Icon -->
                <?php if ($showMiniCart): ?>
                    <button
                <?php else: ?>
                    <a
                <?php endif ?>
                    id="menu-cart-icon"
                    class="relative inline-block rounded-full p-2 <?= $isHomepage ? 'text-white hover:bg-white hover:bg-opacity-20' : 'text-gray-700 hover:bg-gray-100' ?> transition-colors duration-200"
                    x-ref="cartButton"
                    :aria-disabled="isCartEmpty()"
                    :aria-label="`
                        <?= $escaper->escapeHtmlAttr($showMiniCart ? __('Toggle minicart') : __('View cart')) ?>,
                        ${isCartEmpty()
                            ? '<?= $escaper->escapeHtmlAttr(__('Cart is empty')) ?>'
                            : cart.summary_count > 1
                                ? hyva.str('<?= $escaper->escapeHtmlAttr(__('%1 items')) ?>', cart.summary_count)
                                : hyva.str('<?= $escaper->escapeHtmlAttr(__('%1 item')) ?>', cart.summary_count)
                        }`"
                    <?php if ($showMiniCart): ?>
                        @click.prevent="() => {
                            $dispatch('toggle-cart', { isOpen: true })
                        }"
                        @toggle-cart.window="toggleCart($event)"
                        :aria-expanded="isCartOpen"
                        aria-haspopup="dialog"
                    <?php else: ?>
                        href="<?= $escaper->escapeUrl($block->getUrl('checkout/cart/index')) ?>"
                        title="<?= $escaper->escapeHtmlAttr(__('View cart')) ?>"
                    <?php endif ?>
                >
                    <?= $heroicons->shoppingCartHtml("w-5 h-5", 20, 20, ["aria-hidden" => "true"]) ?>
                    <span
                        x-text="cart.summary_count"
                        x-show="!isCartEmpty()"
                        x-cloak
                        class="absolute -top-1 -right-1 h-4 px-1.5 py-0.5 rounded-full bg-diamond-blue text-white text-xs font-semibold leading-none text-center"
                        aria-hidden="true"
                    ></span>
                <?php if ($showMiniCart): ?>
                    </button>
                <?php else: ?>
                    </a>
                <?php endif ?>

                <!-- Mobile Menu Toggle -->
                <button
                    class="lg:hidden rounded-full p-2 <?= $isHomepage ? 'text-white hover:bg-white hover:bg-opacity-20' : 'text-gray-700 hover:bg-gray-100' ?> transition-colors duration-200"
                    @click="$dispatch('toggle-mobile-menu')"
                    aria-label="<?= $escaper->escapeHtmlAttr(__('Toggle mobile menu')) ?>"
                >
                    <?= $heroicons->menuHtml("w-5 h-5", 20, 20, ["aria-hidden" => "true"]) ?>
                </button>
            </div>
        </div>
    </div>

    <!-- Search Dropdown -->
    <div class="absolute z-40 w-full border-t shadow-lg bg-white border-gray-200"
         id="search-content"
         x-cloak x-show="searchOpen"
         @click.outside="searchOpen = false"
         @keydown.escape="
            searchOpen = false;
            $refs.searchButton.focus();
         "
    >
        <?= $block->getChildHtml('header-search'); ?>
    </div>

    <!-- Cart Drawer -->
    <?= $block->getChildHtml('cart-drawer'); ?>

    <!-- Authentication Pop-Up -->
    <?= $block->getChildHtml('authentication-popup'); ?>
</div>
