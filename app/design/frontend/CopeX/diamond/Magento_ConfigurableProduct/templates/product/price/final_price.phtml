<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\StoreConfig;

/** @var ViewModelRegistry $viewModels */
/** @var \Magento\ConfigurableProduct\Pricing\Render\FinalPriceBox$block */
/** @var \Magento\Framework\Pricing\Price\PriceInterface $priceModel */
$priceModel = $block->getPriceType('regular_price');

$finalPriceModel = $block->getPriceType('final_price');

/** @var ViewModelRegistry $viewModels */

/** @var StoreConfig $storeConfig */
$storeConfig = $viewModels->require(StoreConfig::class);
$idSuffix = $block->getIdSuffix() ? $block->getIdSuffix() : '';
$schema = ($block->getZone() == 'item_view') ? true : false;
?>
<span class="normal-price">
    <?= /* @noEscape */ $block->renderAmount($finalPriceModel->getAmount(), [
        'price_id' => $block->getPriceId('product-price-' . $idSuffix),
        'price_type' => 'finalPrice',
        'include_container' => true,
        'schema' => $schema,
    ]);
?>
</span>

<?php if ( ($finalPriceModel->getValue() < $priceModel->getValue()) &&
           (!$block->isProductList() || $storeConfig->getStoreConfig("theme_green/category_page/show_old_price"))): ?>
    <span class="price-wrapper title-font font-extralight text-primary-lighter flex gap-1">
        <span class="price-label"><?= __('Regular Price') ?></span>
        <span class="line-through ">
            <?= /* @noEscape */ $block->renderAmount($priceModel->getAmount(), [
                'price_id'          => $block->getPriceId('old-price-' . $idSuffix),
                'price_type'        => 'oldPrice',
                'include_container' => false,
                'skip_adjustments'  => true
            ]); ?>
        </span>
    </span>
<?php endif; ?>

<?php if ($block->showMinimalPrice()) : ?>
    <?php if ($block->getUseLinkForAsLowAs()) :?>
        <a href="<?= $block->escapeUrl($block->getSaleableItem()->getProductUrl()) ?>" class="minimal-price-link">
            <?= /* @noEscape */ $block->renderAmountMinimal() ?>
        </a>
    <?php else :?>
        <span class="minimal-price-link">
            <?= /* @noEscape */ $block->renderAmountMinimal() ?>
        </span>
    <?php endif?>
<?php endif; ?>
