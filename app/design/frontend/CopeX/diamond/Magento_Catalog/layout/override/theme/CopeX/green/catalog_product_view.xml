<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <move element="breadcrumbs" destination="product.info" />
    <referenceBlock name="product.info.delivery" remove="true"/>
    <referenceBlock name="product.attributes.wrapper">
        <action method="setTemplate">
            <argument name="template" xsi:type="string">Magento_Theme::html/section_wrapper.phtml</argument>
        </action>
    </referenceBlock>
    <referenceBlock name="product.info">
        <block class="Magento\Framework\View\Element\Template" name="product.info.datasheet"
               template="Magento_Catalog::product/view/datasheet.phtml">
            <arguments>
                <argument name="sort_order" xsi:type="string">1</argument>
                <argument translate="true" name="title" xsi:type="string">More Information</argument>
            </arguments>
        </block>
    </referenceBlock>
    <referenceBlock name="product.detail.page">
        <container name="product.media.after" htmlTag="div" htmlClass="absolute top-6 right-6 z-10 flex gap-2"/>
    </referenceBlock>
    <referenceBlock name="product.info.details">
        <action method="setTemplate">
            <argument name="template" xsi:type="string">Magento_Catalog::product/view/content.phtml</argument>
        </action>
    </referenceBlock>
    <move element="product.attributes.wrapper" destination="product.info.details"/>
    <referenceBlock name="product.info.details.wrapper.main" remove="true" />
    <move element="product.info.details" destination="product.detail.page" />
    <move element="review_list" destination="product.info.details" />
    <move element="product.review.form" destination="product.info.details" />
</page>
