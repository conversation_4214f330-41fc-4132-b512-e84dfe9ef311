<?php

use CopeX\HyvaTheme\ViewModel\Cms;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\ProductPage;
use Magento\Framework\View\Element\Template;
use Diamond\Theme\ViewModel\SafeAttribute;
use Magento\Framework\Escaper;

/** @var Template $block */
/** @var $escaper Escaper */
/** @var ViewModelRegistry $viewModels */

/** @var Cms $cmsHelper */
$cmsHelper = $viewModels->require(Cms::class);


/** @var ProductPage $product */
$productViewModel = $viewModels->require(ProductPage::class);
$product = $productViewModel->getProduct();

/** @var SafeAttribute $productViewModel */
$productViewModel = $viewModels->require(SafeAttribute::class);

$productAttributes = $productViewModel->getAttributesByGroup('Data Sheet');


?>
<?php if ($productAttributes):  ?>
    <div class="my-10">
        <?php if ($block->getTitle()): ?>
            <h5><?= $escaper->escapeHtml($block->getTitle()); ?></h5>
        <?php endif; ?>

        <table class="w-full">
            <tbody>
            <?php
            $i = 0; // use counter $i instead of array $key since an attribute might not hold value
            foreach ($productAttributes as $attribute) {
                $attributeData = $productViewModel->getAttributeData($attribute, $product);
                $output = $cmsHelper->renderBlockContent($attributeData['value'] ?? "");
                if ($output && $attribute->getIsVisibleOnFront()) {

                    $label = $attributeData['label'];
                    ?>
                    <tr class="<?= ($i++ % 2) ? 'bg-gray-200' : ''; ?>">
                        <td class="p-2"><?= $escaper->escapeHtml(__($label)) ?>:</td>
                        <td class="p-2"><?= $escaper->escapeHtml($output) ?></td>
                    </tr>
                    <?php
                }
            }
            ?>
            </tbody>
        </table>
    </div>
<?php endif; ?>
