<?php

use CopeX\HyvaTheme\ViewModel\Cms;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\ProductPage;
use Magento\Framework\View\Element\Template;
use Diamond\Theme\ViewModel\SafeAttribute;

/** @var Template $block */

/** @var ViewModelRegistry $viewModels */

/** @var Cms $cmsHelper */
$cmsHelper = $viewModels->require(Cms::class);


/** @var ProductPage $product */
$productViewModel = $viewModels->require(ProductPage::class);
$product = $productViewModel->getProduct();

/** @var SafeAttribute $productViewModel */
$productViewModel = $viewModels->require(SafeAttribute::class);

$productAttributes = $productViewModel->getAttributesByGroup('Tabs');

$wrapperBlock = $block->getLayout()->getBlock('section.wrapper');
?>

<?= $block->getChildHtml('product.info.details'); ?>
<?= $block->getChildHtml('product.attributes.wrapper'); ?>

<?php
if ($productAttributes) {
    foreach($productAttributes as $attribute) {
        $attributeData = $productViewModel->getAttributeData($attribute, $product);
        
        // SafeAttribute ViewModel already handles array conversion safely
        $output = $cmsHelper->renderBlockContent($attributeData['value'] ?? "");
        if ($output) {
            $wrapperBlock->setTitle($attributeData['label']);
            $wrapperBlock->setContent($output);
            echo $wrapperBlock->toHtml();
        }
    }
}
?>
<?php
$attachments = $block->getLayout()->getBlock('productattach.tab');
if ($attachments) {
    if ($output = $attachments->toHtml()) {
        $wrapperBlock->setTitle($attachments->getTitle());
        $wrapperBlock->setContent($output);
        echo $wrapperBlock->toHtml();
    }
}
?>
<?php
$wrapperBlock->setTitle(__("Top customer reviews"));
$wrapperBlock->setContent($block->getChildHtml('review_list') . $block->getChildHtml('review_form'));
echo $wrapperBlock->toHtml();
?>
