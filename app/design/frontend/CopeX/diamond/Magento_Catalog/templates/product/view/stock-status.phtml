<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\CurrentProduct;
use Magento\Catalog\Model\Product;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

// phpcs:disable Magento2.Templates.ThisInTemplate.FoundThis

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var CurrentProduct $currentProduct */
$currentProduct = $viewModels->require(CurrentProduct::class);

/** @var \Diamond\Theme\ViewModel\SafeAttribute $productViewModel */
$productViewModel = $viewModels->require(\Diamond\Theme\ViewModel\SafeAttribute::class);

/** @var Product $product */
$product = $block->hasData('product')
    ? $block->getData('product')
    : $currentProduct->get();

if (!$product || !$product->getId()) {
    return;
}
?>
<?php if ($block->getParentBlock()->displayProductStockStatus()): ?>
    <div class="text-left md:text-right">
        <?php if ($product->getIsSalable()): ?>
            <p class="fixed bottom-0 pb-16 z-20 md:pb-0 md:z-0 md:relative flex items-center my-4 align-middle available gap-x-2 text-sm"
               title="<?= $escaper->escapeHtmlAttr(__('Availability')) ?>">
                <span class="price-in-stock"><?= $escaper->escapeHtml(__('IN STOCK')) ?><span class="hidden md:inline"> /</span></span>
                <?php $deliveryTime = $productViewModel->getAttributeData('delivery_time',$product); ?>
                <?php if($deliveryTime): ?>
                    <span class="hidden md:inline"><span class="mr-2"><?= $escaper->escapeHtmlAttr(__('LEAD TIME')) ?> </span><span><?= $deliveryTime['value']; ?></span></span>
                <?php endif; ?>
            </p>
        <?php else: ?>
            <p class="flex items-centeralign-middle gap-x-2 unavailable text-quaternary"
               title="<?= $escaper->escapeHtmlAttr(__('Availability')) ?>">
                <span class="w-3 h-3 bg-secondary rounded-full shrink-0"></span>
                <span><?= $escaper->escapeHtml(__('Out of stock')) ?></span>
            </p>
        <?php endif; ?>
    </div>
<?php endif; ?>
