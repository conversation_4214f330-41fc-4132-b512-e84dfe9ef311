<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use CopeX\HyvaTheme\ViewModel\Cms;
use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Catalog\Block\Product\View;
use Hyva\Theme\ViewModel\DeployMode;
use Hyva\Theme\ViewModel\StoreConfig;
use CopeX\ThemeGreen\Model\Config\Source\Page\LazyLoad;

/** @var View $block */

$product = $block->getProduct();

/** @var ViewModelRegistry $viewModels */

/** @var Cms $cmsHelper */
$cmsHelper = $viewModels->require(Cms::class);

$usps = $cmsHelper->renderBlockByIdentifier('product_detail_usps');

/** @var StoreConfig $storeConfig */
$storeConfig = $viewModels->require(StoreConfig::class);

/** @var DeployMode $deployMode */
$deployMode = $viewModels->require(DeployMode::class);

$lazyLoad = $storeConfig->getStoreConfig('theme_green/product_detail_page/lazyload');

?>
<section class="body-font">
    <div class="flex pb-6 lg:flex-row flex-col items-center">
        <div class="flex flex-wrap order-first w-full">
            <div id="gallery" class="relative order-1 w-full lg:w-2/3 md:h-auto pr-0 lg:pr-6">
                <?= $block->getChildHtml('product.media') ?>
                <?= $block->getChildHtml('product.media.after') ?>
            </div>
            <?= $block->getChildHtml('product.info') ?>
        </div>
    </div>
</section>
<?php $optionsWrapperBottom = $block->getChildHtml('product_options_wrapper_bottom'); ?>
<?php if($optionsWrapperBottom): ?>
    <section>
        <?= $optionsWrapperBottom ?>
    </section>
<?php endif; ?>
<?= $usps ?>
<section id="product-details" class="rounded-sm shadow bg-white mt-2 md:mt-6">
    <?php if($lazyLoad == LazyLoad::ENABLED || ($lazyLoad == LazyLoad::PRODUCTION && !$deployMode->isDeveloperMode())): ?>
        <script>
            function lazyLoadProductDetails(){
                fetch("<?= $block->getUrl('lazyloadhtml/product/get') ?>id/<?=  $product->getId() ?>", {
                    method: "GET",
                    async: true,
                    cache: "default",
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                }).then(response => response.json()).then((data) => {
                    if (!data.details) return false;
                    let productDetails = document.getElementById('product-details');
                    productDetails.innerHTML = data.details;
                    let formKey = hyva.getFormKey();
                    Array.from(productDetails.querySelectorAll('input[name="form_key"]')).map((input) => input.value = formKey);
                    hyva.updateContentFromTemplates(productDetails);
                });
            }
            window.addEventListener('init-external-scripts',lazyLoadProductDetails);
        </script>
    <?php elseif($lazyLoad  == LazyLoad::ALPINE): ?>
        <div x-data="{ loaded: false }" x-init="$nextTick(() => { loaded=true; })">
            <template x-if="loaded">
                <div x-data="{}" x-init="hyva.updateContentFromTemplates($el)">
                    <?= $block->getChildHtml('product.info.details');  ?>
                </div>
            </template>
        </div>
    <?php else: ?>
        <?= $block->getChildHtml('product.info.details');  ?>
    <?php endif; ?>
    <?= $block->getChildHtml("product.info.datasheet") ?>
</section>
<section>
    <?= $block->getChildHtml('related') ?>
    <?= $block->getChildHtml('upsell') ?>
</section>
