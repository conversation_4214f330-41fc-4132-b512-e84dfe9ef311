/* purgecss start ignore */

/* ! tailwindcss v3.2.4 | MIT License | https://tailwindcss.com */

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box;
  /* 1 */
  border-width: 0;
  /* 2 */
  border-style: solid;
  /* 2 */
  border-color: #e5e7eb;
  /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
*/

html {
  line-height: 1.5;
  /* 1 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
  -moz-tab-size: 4;
  /* 3 */
  tab-size: 4;
  /* 3 */
  font-family: Univers, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  /* 4 */
  font-feature-settings: normal;
  /* 5 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0;
  /* 1 */
  line-height: inherit;
  /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0;
  /* 1 */
  color: inherit;
  /* 2 */
  border-top-width: 1px;
  /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font family by default.
2. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  /* 1 */
  font-size: 1em;
  /* 2 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0;
  /* 1 */
  border-color: inherit;
  /* 2 */
  border-collapse: collapse;
  /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  /* 1 */
  font-size: 100%;
  /* 1 */
  font-weight: inherit;
  /* 1 */
  line-height: inherit;
  /* 1 */
  color: inherit;
  /* 1 */
  margin: 0;
  /* 2 */
  padding: 0;
  /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
[type='button'],
[type='reset'],
[type='submit'] {
  -webkit-appearance: button;
  /* 1 */
  background-color: transparent;
  /* 2 */
  background-image: none;
  /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield;
  /* 1 */
  outline-offset: -2px;
  /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input:-ms-input-placeholder, textarea:-ms-input-placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block;
  /* 1 */
  vertical-align: middle;
  /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden] {
  display: none;
}

[type='text'],[type='email'],[type='url'],[type='password'],[type='number'],[type='date'],[type='datetime-local'],[type='month'],[type='search'],[type='tel'],[type='time'],[type='week'],[multiple],textarea,select {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background-color: #fff;
  border-color: #6b7280;
  border-width: 1px;
  border-radius: 0px;
  padding-top: 0.5rem;
  padding-right: 0.75rem;
  padding-bottom: 0.5rem;
  padding-left: 0.75rem;
  font-size: 1rem;
  line-height: 1.5rem;
  --tw-shadow: 0 0 #0000;
}

[type='text']:focus, [type='email']:focus, [type='url']:focus, [type='password']:focus, [type='number']:focus, [type='date']:focus, [type='datetime-local']:focus, [type='month']:focus, [type='search']:focus, [type='tel']:focus, [type='time']:focus, [type='week']:focus, [multiple]:focus, textarea:focus, select:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #00467e;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  border-color: #00467e;
}

input:-ms-input-placeholder, textarea:-ms-input-placeholder {
  color: #6b7280;
  opacity: 1;
}

input::placeholder,textarea::placeholder {
  color: #6b7280;
  opacity: 1;
}

::-webkit-datetime-edit-fields-wrapper {
  padding: 0;
}

::-webkit-date-and-time-value {
  min-height: 1.5em;
}

::-webkit-datetime-edit,::-webkit-datetime-edit-year-field,::-webkit-datetime-edit-month-field,::-webkit-datetime-edit-day-field,::-webkit-datetime-edit-hour-field,::-webkit-datetime-edit-minute-field,::-webkit-datetime-edit-second-field,::-webkit-datetime-edit-millisecond-field,::-webkit-datetime-edit-meridiem-field {
  padding-top: 0;
  padding-bottom: 0;
}

select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
  -webkit-print-color-adjust: exact;
     color-adjust: exact;
          print-color-adjust: exact;
}

[multiple] {
  background-image: initial;
  background-position: initial;
  background-repeat: unset;
  background-size: initial;
  padding-right: 0.75rem;
  -webkit-print-color-adjust: unset;
     color-adjust: unset;
          print-color-adjust: unset;
}

[type='checkbox'],[type='radio'] {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  padding: 0;
  -webkit-print-color-adjust: exact;
     color-adjust: exact;
          print-color-adjust: exact;
  display: inline-block;
  vertical-align: middle;
  background-origin: border-box;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  flex-shrink: 0;
  height: 1rem;
  width: 1rem;
  color: #00467e;
  background-color: #fff;
  border-color: #6b7280;
  border-width: 1px;
  --tw-shadow: 0 0 #0000;
}

[type='checkbox'] {
  border-radius: 0px;
}

[type='radio'] {
  border-radius: 100%;
}

[type='checkbox']:focus,[type='radio']:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 2px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #00467e;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

[type='checkbox']:checked,[type='radio']:checked {
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

[type='checkbox']:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e");
}

[type='radio']:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e");
}

[type='checkbox']:checked:hover,[type='checkbox']:checked:focus,[type='radio']:checked:hover,[type='radio']:checked:focus {
  border-color: transparent;
  background-color: currentColor;
}

[type='checkbox']:indeterminate {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 16'%3e%3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 8h8'/%3e%3c/svg%3e");
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

[type='checkbox']:indeterminate:hover,[type='checkbox']:indeterminate:focus {
  border-color: transparent;
  background-color: currentColor;
}

[type='file'] {
  background: unset;
  border-color: inherit;
  border-width: 0;
  border-radius: 0;
  padding: 0;
  font-size: unset;
  line-height: inherit;
}

[type='file']:focus {
  outline: 1px solid ButtonText;
  outline: 1px auto -webkit-focus-ring-color;
}

h1,.h1, h2, .h2, h3, .h3, h4, .h4, h5, .h5, h6, .h6 {
  margin-bottom: 2rem;
  margin-top: 2rem;
  overflow-wrap: break-word;
  font-weight: 500;
}

h1, .h1 {
  font-size: 2rem;
  line-height: 2rem;
}

h2, .h2 {
  font-size: 1.8rem;
  line-height: 1.8rem;
  margin-top: 2rem;
}

h3, .h3 {
  font-size: 1.6rem;
  line-height: 1.6rem;
  margin-bottom: 1rem;
}

h4, .h4 {
  font-size: 1.4rem;
}

h5, .h5 {
  font-size: 1.2rem;
}

h6, .h6 {
  font-size: 1rem;
}

h1.page-title{
  padding-bottom: 0.5rem;
}

*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(14 165 233 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(14 165 233 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
}

.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 1.5rem;
  padding-left: 1.5rem;
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

@media (min-width: 1770px) {
  .container {
    max-width: 1770px;
  }
}

.form-input,.form-textarea,.form-select,.form-multiselect {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  background-color: #fff;
  border-color: #6b7280;
  border-width: 1px;
  border-radius: 0px;
  padding-top: 0.5rem;
  padding-right: 0.75rem;
  padding-bottom: 0.5rem;
  padding-left: 0.75rem;
  font-size: 1rem;
  line-height: 1.5rem;
  --tw-shadow: 0 0 #0000;
}

.form-input:focus, .form-textarea:focus, .form-select:focus, .form-multiselect:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #00467e;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  border-color: #00467e;
}

.form-input:-ms-input-placeholder, .form-textarea:-ms-input-placeholder {
  color: #6b7280;
  opacity: 1;
}

.form-input::placeholder,.form-textarea::placeholder {
  color: #6b7280;
  opacity: 1;
}

.form-input::-webkit-datetime-edit-fields-wrapper {
  padding: 0;
}

.form-input::-webkit-date-and-time-value {
  min-height: 1.5em;
}

.form-input::-webkit-datetime-edit,.form-input::-webkit-datetime-edit-year-field,.form-input::-webkit-datetime-edit-month-field,.form-input::-webkit-datetime-edit-day-field,.form-input::-webkit-datetime-edit-hour-field,.form-input::-webkit-datetime-edit-minute-field,.form-input::-webkit-datetime-edit-second-field,.form-input::-webkit-datetime-edit-millisecond-field,.form-input::-webkit-datetime-edit-meridiem-field {
  padding-top: 0;
  padding-bottom: 0;
}

.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
  -webkit-print-color-adjust: exact;
     color-adjust: exact;
          print-color-adjust: exact;
}

.form-checkbox,.form-radio {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  padding: 0;
  -webkit-print-color-adjust: exact;
     color-adjust: exact;
          print-color-adjust: exact;
  display: inline-block;
  vertical-align: middle;
  background-origin: border-box;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  flex-shrink: 0;
  height: 1rem;
  width: 1rem;
  color: #00467e;
  background-color: #fff;
  border-color: #6b7280;
  border-width: 1px;
  --tw-shadow: 0 0 #0000;
}

.form-checkbox {
  border-radius: 0px;
}

.form-radio {
  border-radius: 100%;
}

.form-checkbox:focus,.form-radio:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
  --tw-ring-offset-width: 2px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: #00467e;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
}

.form-checkbox:checked,.form-radio:checked {
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

.form-checkbox:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e");
}

.form-radio:checked {
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e");
}

.form-checkbox:checked:hover,.form-checkbox:checked:focus,.form-radio:checked:hover,.form-radio:checked:focus {
  border-color: transparent;
  background-color: currentColor;
}

.form-checkbox:indeterminate {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 16'%3e%3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 8h8'/%3e%3c/svg%3e");
  border-color: transparent;
  background-color: currentColor;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
}

.form-checkbox:indeterminate:hover,.form-checkbox:indeterminate:focus {
  border-color: transparent;
  background-color: currentColor;
}

.prose {
  color: var(--tw-prose-body);
  max-width: 65ch;
}

.prose :where([class~="lead"]):not(:where([class~="not-prose"] *)) {
  color: var(--tw-prose-lead);
  font-size: 1.25em;
  line-height: 1.6;
  margin-top: 1.2em;
  margin-bottom: 1.2em;
}

.prose :where(a):not(:where([class~="not-prose"] *)) {
  color: var(--tw-prose-links);
  text-decoration: underline;
  font-weight: 500;
}

.prose :where(strong):not(:where([class~="not-prose"] *)) {
  color: var(--tw-prose-bold);
  font-weight: 600;
}

.prose :where(a strong):not(:where([class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(blockquote strong):not(:where([class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(thead th strong):not(:where([class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(ol):not(:where([class~="not-prose"] *)) {
  list-style-type: decimal;
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  padding-left: 1.625em;
}

.prose :where(ol[type="A"]):not(:where([class~="not-prose"] *)) {
  list-style-type: upper-alpha;
}

.prose :where(ol[type="a"]):not(:where([class~="not-prose"] *)) {
  list-style-type: lower-alpha;
}

.prose :where(ol[type="A" s]):not(:where([class~="not-prose"] *)) {
  list-style-type: upper-alpha;
}

.prose :where(ol[type="a" s]):not(:where([class~="not-prose"] *)) {
  list-style-type: lower-alpha;
}

.prose :where(ol[type="I"]):not(:where([class~="not-prose"] *)) {
  list-style-type: upper-roman;
}

.prose :where(ol[type="i"]):not(:where([class~="not-prose"] *)) {
  list-style-type: lower-roman;
}

.prose :where(ol[type="I" s]):not(:where([class~="not-prose"] *)) {
  list-style-type: upper-roman;
}

.prose :where(ol[type="i" s]):not(:where([class~="not-prose"] *)) {
  list-style-type: lower-roman;
}

.prose :where(ol[type="1"]):not(:where([class~="not-prose"] *)) {
  list-style-type: decimal;
}

.prose :where(ul):not(:where([class~="not-prose"] *)) {
  list-style-type: disc;
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  padding-left: 1.625em;
}

.prose :where(ol > li):not(:where([class~="not-prose"] *))::marker {
  font-weight: 400;
  color: var(--tw-prose-counters);
}

.prose :where(ul > li):not(:where([class~="not-prose"] *))::marker {
  color: var(--tw-prose-bullets);
}

.prose :where(hr):not(:where([class~="not-prose"] *)) {
  border-color: var(--tw-prose-hr);
  border-top-width: 1px;
  margin-top: 3em;
  margin-bottom: 3em;
}

.prose :where(blockquote):not(:where([class~="not-prose"] *)) {
  font-weight: 500;
  font-style: italic;
  color: var(--tw-prose-quotes);
  border-left-width: 0.25rem;
  border-left-color: var(--tw-prose-quote-borders);
  quotes: "\201C""\201D""\2018""\2019";
  margin-top: 1.6em;
  margin-bottom: 1.6em;
  padding-left: 1em;
}

.prose :where(blockquote p:first-of-type):not(:where([class~="not-prose"] *))::before {
  content: open-quote;
}

.prose :where(blockquote p:last-of-type):not(:where([class~="not-prose"] *))::after {
  content: close-quote;
}

.prose :where(h1):not(:where([class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 800;
  font-size: 2.25em;
  margin-top: 0;
  margin-bottom: 0.8888889em;
  line-height: 1.1111111;
}

.prose :where(h1 strong):not(:where([class~="not-prose"] *)) {
  font-weight: 900;
  color: inherit;
}

.prose :where(h2):not(:where([class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 700;
  font-size: 1.5em;
  margin-top: 2em;
  margin-bottom: 1em;
  line-height: 1.3333333;
}

.prose :where(h2 strong):not(:where([class~="not-prose"] *)) {
  font-weight: 800;
  color: inherit;
}

.prose :where(h3):not(:where([class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  font-size: 1.25em;
  margin-top: 1.6em;
  margin-bottom: 0.6em;
  line-height: 1.6;
}

.prose :where(h3 strong):not(:where([class~="not-prose"] *)) {
  font-weight: 700;
  color: inherit;
}

.prose :where(h4):not(:where([class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  line-height: 1.5;
}

.prose :where(h4 strong):not(:where([class~="not-prose"] *)) {
  font-weight: 700;
  color: inherit;
}

.prose :where(img):not(:where([class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}

.prose :where(figure > *):not(:where([class~="not-prose"] *)) {
  margin-top: 0;
  margin-bottom: 0;
}

.prose :where(figcaption):not(:where([class~="not-prose"] *)) {
  color: var(--tw-prose-captions);
  font-size: 0.875em;
  line-height: 1.4285714;
  margin-top: 0.8571429em;
}

.prose :where(code):not(:where([class~="not-prose"] *)) {
  color: var(--tw-prose-code);
  font-weight: 600;
  font-size: 0.875em;
}

.prose :where(code):not(:where([class~="not-prose"] *))::before {
  content: "`";
}

.prose :where(code):not(:where([class~="not-prose"] *))::after {
  content: "`";
}

.prose :where(a code):not(:where([class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(h1 code):not(:where([class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(h2 code):not(:where([class~="not-prose"] *)) {
  color: inherit;
  font-size: 0.875em;
}

.prose :where(h3 code):not(:where([class~="not-prose"] *)) {
  color: inherit;
  font-size: 0.9em;
}

.prose :where(h4 code):not(:where([class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(blockquote code):not(:where([class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(thead th code):not(:where([class~="not-prose"] *)) {
  color: inherit;
}

.prose :where(pre):not(:where([class~="not-prose"] *)) {
  color: var(--tw-prose-pre-code);
  background-color: var(--tw-prose-pre-bg);
  overflow-x: auto;
  font-weight: 400;
  font-size: 0.875em;
  line-height: 1.7142857;
  margin-top: 1.7142857em;
  margin-bottom: 1.7142857em;
  border-radius: 0.375rem;
  padding-top: 0.8571429em;
  padding-right: 1.1428571em;
  padding-bottom: 0.8571429em;
  padding-left: 1.1428571em;
}

.prose :where(pre code):not(:where([class~="not-prose"] *)) {
  background-color: transparent;
  border-width: 0;
  border-radius: 0;
  padding: 0;
  font-weight: inherit;
  color: inherit;
  font-size: inherit;
  font-family: inherit;
  line-height: inherit;
}

.prose :where(pre code):not(:where([class~="not-prose"] *))::before {
  content: none;
}

.prose :where(pre code):not(:where([class~="not-prose"] *))::after {
  content: none;
}

.prose :where(table):not(:where([class~="not-prose"] *)) {
  width: 100%;
  table-layout: auto;
  text-align: left;
  margin-top: 2em;
  margin-bottom: 2em;
  font-size: 0.875em;
  line-height: 1.7142857;
}

.prose :where(thead):not(:where([class~="not-prose"] *)) {
  border-bottom-width: 1px;
  border-bottom-color: var(--tw-prose-th-borders);
}

.prose :where(thead th):not(:where([class~="not-prose"] *)) {
  color: var(--tw-prose-headings);
  font-weight: 600;
  vertical-align: bottom;
  padding-right: 0.5714286em;
  padding-bottom: 0.5714286em;
  padding-left: 0.5714286em;
}

.prose :where(tbody tr):not(:where([class~="not-prose"] *)) {
  border-bottom-width: 1px;
  border-bottom-color: var(--tw-prose-td-borders);
}

.prose :where(tbody tr:last-child):not(:where([class~="not-prose"] *)) {
  border-bottom-width: 0;
}

.prose :where(tbody td):not(:where([class~="not-prose"] *)) {
  vertical-align: baseline;
}

.prose :where(tfoot):not(:where([class~="not-prose"] *)) {
  border-top-width: 1px;
  border-top-color: var(--tw-prose-th-borders);
}

.prose :where(tfoot td):not(:where([class~="not-prose"] *)) {
  vertical-align: top;
}

.prose {
  --tw-prose-body: #374151;
  --tw-prose-headings: #111827;
  --tw-prose-lead: #4b5563;
  --tw-prose-links: #111827;
  --tw-prose-bold: #111827;
  --tw-prose-counters: #6b7280;
  --tw-prose-bullets: #d1d5db;
  --tw-prose-hr: #e5e7eb;
  --tw-prose-quotes: #111827;
  --tw-prose-quote-borders: #e5e7eb;
  --tw-prose-captions: #6b7280;
  --tw-prose-code: #111827;
  --tw-prose-pre-code: #e5e7eb;
  --tw-prose-pre-bg: #1f2937;
  --tw-prose-th-borders: #d1d5db;
  --tw-prose-td-borders: #e5e7eb;
  --tw-prose-invert-body: #d1d5db;
  --tw-prose-invert-headings: #fff;
  --tw-prose-invert-lead: #9ca3af;
  --tw-prose-invert-links: #fff;
  --tw-prose-invert-bold: #fff;
  --tw-prose-invert-counters: #9ca3af;
  --tw-prose-invert-bullets: #4b5563;
  --tw-prose-invert-hr: #374151;
  --tw-prose-invert-quotes: #f3f4f6;
  --tw-prose-invert-quote-borders: #374151;
  --tw-prose-invert-captions: #9ca3af;
  --tw-prose-invert-code: #fff;
  --tw-prose-invert-pre-code: #d1d5db;
  --tw-prose-invert-pre-bg: rgb(0 0 0 / 50%);
  --tw-prose-invert-th-borders: #4b5563;
  --tw-prose-invert-td-borders: #374151;
  font-size: 1rem;
  line-height: 1.75;
}

.prose :where(p):not(:where([class~="not-prose"] *)) {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
}

.prose :where(video):not(:where([class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}

.prose :where(figure):not(:where([class~="not-prose"] *)) {
  margin-top: 2em;
  margin-bottom: 2em;
}

.prose :where(li):not(:where([class~="not-prose"] *)) {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

.prose :where(ol > li):not(:where([class~="not-prose"] *)) {
  padding-left: 0.375em;
}

.prose :where(ul > li):not(:where([class~="not-prose"] *)) {
  padding-left: 0.375em;
}

.prose :where(.prose > ul > li p):not(:where([class~="not-prose"] *)) {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}

.prose :where(.prose > ul > li > *:first-child):not(:where([class~="not-prose"] *)) {
  margin-top: 1.25em;
}

.prose :where(.prose > ul > li > *:last-child):not(:where([class~="not-prose"] *)) {
  margin-bottom: 1.25em;
}

.prose :where(.prose > ol > li > *:first-child):not(:where([class~="not-prose"] *)) {
  margin-top: 1.25em;
}

.prose :where(.prose > ol > li > *:last-child):not(:where([class~="not-prose"] *)) {
  margin-bottom: 1.25em;
}

.prose :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"] *)) {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}

.prose :where(hr + *):not(:where([class~="not-prose"] *)) {
  margin-top: 0;
}

.prose :where(h2 + *):not(:where([class~="not-prose"] *)) {
  margin-top: 0;
}

.prose :where(h3 + *):not(:where([class~="not-prose"] *)) {
  margin-top: 0;
}

.prose :where(h4 + *):not(:where([class~="not-prose"] *)) {
  margin-top: 0;
}

.prose :where(thead th:first-child):not(:where([class~="not-prose"] *)) {
  padding-left: 0;
}

.prose :where(thead th:last-child):not(:where([class~="not-prose"] *)) {
  padding-right: 0;
}

.prose :where(tbody td, tfoot td):not(:where([class~="not-prose"] *)) {
  padding-top: 0.5714286em;
  padding-right: 0.5714286em;
  padding-bottom: 0.5714286em;
  padding-left: 0.5714286em;
}

.prose :where(tbody td:first-child, tfoot td:first-child):not(:where([class~="not-prose"] *)) {
  padding-left: 0;
}

.prose :where(tbody td:last-child, tfoot td:last-child):not(:where([class~="not-prose"] *)) {
  padding-right: 0;
}

.prose :where(.prose > :first-child):not(:where([class~="not-prose"] *)) {
  margin-top: 0;
}

.prose :where(.prose > :last-child):not(:where([class~="not-prose"] *)) {
  margin-bottom: 0;
}

.prose-sm :where(.prose > ul > li p):not(:where([class~="not-prose"] *)) {
  margin-top: 0.5714286em;
  margin-bottom: 0.5714286em;
}

.prose-sm :where(.prose > ul > li > *:first-child):not(:where([class~="not-prose"] *)) {
  margin-top: 1.1428571em;
}

.prose-sm :where(.prose > ul > li > *:last-child):not(:where([class~="not-prose"] *)) {
  margin-bottom: 1.1428571em;
}

.prose-sm :where(.prose > ol > li > *:first-child):not(:where([class~="not-prose"] *)) {
  margin-top: 1.1428571em;
}

.prose-sm :where(.prose > ol > li > *:last-child):not(:where([class~="not-prose"] *)) {
  margin-bottom: 1.1428571em;
}

.prose-sm :where(.prose > :first-child):not(:where([class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-sm :where(.prose > :last-child):not(:where([class~="not-prose"] *)) {
  margin-bottom: 0;
}

.prose-base :where(.prose > ul > li p):not(:where([class~="not-prose"] *)) {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}

.prose-base :where(.prose > ul > li > *:first-child):not(:where([class~="not-prose"] *)) {
  margin-top: 1.25em;
}

.prose-base :where(.prose > ul > li > *:last-child):not(:where([class~="not-prose"] *)) {
  margin-bottom: 1.25em;
}

.prose-base :where(.prose > ol > li > *:first-child):not(:where([class~="not-prose"] *)) {
  margin-top: 1.25em;
}

.prose-base :where(.prose > ol > li > *:last-child):not(:where([class~="not-prose"] *)) {
  margin-bottom: 1.25em;
}

.prose-base :where(.prose > :first-child):not(:where([class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-base :where(.prose > :last-child):not(:where([class~="not-prose"] *)) {
  margin-bottom: 0;
}

.prose-lg :where(.prose > ul > li p):not(:where([class~="not-prose"] *)) {
  margin-top: 0.8888889em;
  margin-bottom: 0.8888889em;
}

.prose-lg :where(.prose > ul > li > *:first-child):not(:where([class~="not-prose"] *)) {
  margin-top: 1.3333333em;
}

.prose-lg :where(.prose > ul > li > *:last-child):not(:where([class~="not-prose"] *)) {
  margin-bottom: 1.3333333em;
}

.prose-lg :where(.prose > ol > li > *:first-child):not(:where([class~="not-prose"] *)) {
  margin-top: 1.3333333em;
}

.prose-lg :where(.prose > ol > li > *:last-child):not(:where([class~="not-prose"] *)) {
  margin-bottom: 1.3333333em;
}

.prose-lg :where(.prose > :first-child):not(:where([class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-lg :where(.prose > :last-child):not(:where([class~="not-prose"] *)) {
  margin-bottom: 0;
}

.prose-xl :where(.prose > ul > li p):not(:where([class~="not-prose"] *)) {
  margin-top: 0.8em;
  margin-bottom: 0.8em;
}

.prose-xl :where(.prose > ul > li > *:first-child):not(:where([class~="not-prose"] *)) {
  margin-top: 1.2em;
}

.prose-xl :where(.prose > ul > li > *:last-child):not(:where([class~="not-prose"] *)) {
  margin-bottom: 1.2em;
}

.prose-xl :where(.prose > ol > li > *:first-child):not(:where([class~="not-prose"] *)) {
  margin-top: 1.2em;
}

.prose-xl :where(.prose > ol > li > *:last-child):not(:where([class~="not-prose"] *)) {
  margin-bottom: 1.2em;
}

.prose-xl :where(.prose > :first-child):not(:where([class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-xl :where(.prose > :last-child):not(:where([class~="not-prose"] *)) {
  margin-bottom: 0;
}

.prose-2xl :where(.prose > ul > li p):not(:where([class~="not-prose"] *)) {
  margin-top: 0.8333333em;
  margin-bottom: 0.8333333em;
}

.prose-2xl :where(.prose > ul > li > *:first-child):not(:where([class~="not-prose"] *)) {
  margin-top: 1.3333333em;
}

.prose-2xl :where(.prose > ul > li > *:last-child):not(:where([class~="not-prose"] *)) {
  margin-bottom: 1.3333333em;
}

.prose-2xl :where(.prose > ol > li > *:first-child):not(:where([class~="not-prose"] *)) {
  margin-top: 1.3333333em;
}

.prose-2xl :where(.prose > ol > li > *:last-child):not(:where([class~="not-prose"] *)) {
  margin-bottom: 1.3333333em;
}

.prose-2xl :where(.prose > :first-child):not(:where([class~="not-prose"] *)) {
  margin-top: 0;
}

.prose-2xl :where(.prose > :last-child):not(:where([class~="not-prose"] *)) {
  margin-bottom: 0;
}

.btn {
  display: flex;
  align-items: center;
  --tw-bg-opacity: 1;
  background-color: rgb(42 42 41 / var(--tw-bg-opacity));
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 400;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

@media (min-width: 768px) {
  .btn {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
}

@media (min-width: 1280px) {
  .btn {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}

.btn svg {
  display: inline-flex;
}

.btn span {
  vertical-align: middle;
}

.btn:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(66 66 65 / var(--tw-bg-opacity));
}

.btn:focus {
  border-color: transparent;
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-color: rgb(0 70 126 / var(--tw-ring-opacity));
  --tw-ring-opacity: 0.5;
}

.btn-primary {
  --tw-bg-opacity: 1;
  background-color: rgb(42 42 41 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.btn-primary:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(66 66 65 / var(--tw-bg-opacity));
}

.btn-secondary {
  border-width: 2px;
  --tw-border-opacity: 1;
  border-color: rgb(*********** / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.btn-secondary:hover {
  --tw-border-opacity: 1;
  border-color: rgb(42 42 41 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.btn-secondary:focus {
  border-width: 2px;
  border-color: transparent;
}

.btn-size-lg {
  padding-left: 2.5rem;
  padding-right: 2.5rem;
  padding-top: 1rem;
  padding-bottom: 1rem;
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.actions-toolbar .primary button {
  display: flex;
  align-items: center;
  --tw-bg-opacity: 1;
  background-color: rgb(42 42 41 / var(--tw-bg-opacity));
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 400;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

@media (min-width: 768px) {
  .actions-toolbar .primary button {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
}

@media (min-width: 1280px) {
  .actions-toolbar .primary button {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}

.actions-toolbar .primary button svg {
  display: inline-flex;
}

.actions-toolbar .primary button span {
  vertical-align: middle;
}

.actions-toolbar .primary button:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(66 66 65 / var(--tw-bg-opacity));
}

.actions-toolbar .primary button:focus {
  border-color: transparent;
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-color: rgb(0 70 126 / var(--tw-ring-opacity));
  --tw-ring-opacity: 0.5;
}

.actions-toolbar .primary button {
  --tw-bg-opacity: 1;
  background-color: rgb(42 42 41 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.actions-toolbar .primary button:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(66 66 65 / var(--tw-bg-opacity));
}

/* @import url(components/font.css); - use CopeX_InlineFontLoader instead */

#category-view-container {
  margin-left: auto;
  margin-right: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-left: 0px;
  padding-right: 0px;
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.category-description {
  max-width: 56rem;
}

.toolbar-products .modes-mode {
  height: 1.5rem;
  width: 1.5rem;
}

.toolbar-products .modes-mode span {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.toolbar-products .modes-mode.mode-grid {
  background-image: url('data:image/svg+xml;utf8,<svg  stroke="currentColor" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3.5 3.5H10.5V10.5H3.5V3.5Z"/> <path d="M3.5 13.5H10.5V20.5H3.5V13.5Z"/> <path d="M13.5 3.5H20.5V10.5H13.5V3.5Z"/> <path d="M13.5 13.5H20.5V20.5H13.5V13.5Z"/> </svg>');
}

.toolbar-products .modes-mode.mode-list {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16" /></svg>');
}

.price-including-tax + .price-excluding-tax {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.price-including-tax + .price-excluding-tax:before {
  content: attr(data-label) ': ';
}

.price-excluding-tax,
.price-including-tax {
  display: block;
  white-space: nowrap;
}

[data-content-type$='block'] .price-excluding-tax p:last-child,[data-content-type$='block'] 
.price-including-tax p:last-child {
  margin-bottom: 1rem;
  margin-top: 0px;
}

.category-view .sidebar .price-excluding-tax.filter,.category-view .sidebar 
.price-including-tax.filter {
}

.price-excluding-tax .price, .price-including-tax .price {
  font-weight: 600;
  line-height: 1.625;
}

#customer-login-container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding-top: 2rem;
  padding-bottom: 2rem;
}

@media (min-width: 768px) {
  #customer-login-container {
    flex-direction: row;
  }
}

/**
 * different styles can be found at https://tailwindcss-forms.vercel.app/
 **/

.form-input,
.form-email,
.form-select,
.form-multiselect,
.form-textarea,
[type='text'],
[type='email'],
[type='url'],
[type='password'],
[type='number'],
[type='date'],
[type='datetime-local'],
[type='month'],
[type='search'],
[type='tel'],
[type='time'],
[type='week'],
[multiple],
textarea,
select {
  width: 100%;
  border-radius: 0.125rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(*********** / var(--tw-border-opacity));
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.form-input:focus, .form-email:focus, .form-select:focus, .form-multiselect:focus, .form-textarea:focus, [type='text']:focus, [type='email']:focus, [type='url']:focus, [type='password']:focus, [type='number']:focus, [type='date']:focus, [type='datetime-local']:focus, [type='month']:focus, [type='search']:focus, [type='tel']:focus, [type='time']:focus, [type='week']:focus, [multiple]:focus, textarea:focus, select:focus {
  --tw-border-opacity: 1;
  border-color: rgb(66 66 65 / var(--tw-border-opacity));
  box-shadow: none;
}

.page.messages {
  position: -webkit-sticky;
  position: sticky;
  top: 0px;
  z-index: 20;
}

.page.messages .messages
    section#messages {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 1.5rem;
  padding-left: 1.5rem;
}

@media (min-width: 640px) {
  .page.messages .messages
    section#messages {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .page.messages .messages
    section#messages {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .page.messages .messages
    section#messages {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .page.messages .messages
    section#messages {
    max-width: 1280px;
  }
}

@media (min-width: 1770px) {
  .page.messages .messages
    section#messages {
    max-width: 1770px;
  }
}

.page.messages .messages
    section#messages {
  margin-left: auto;
  margin-right: auto;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.message {
  margin-bottom: 0.5rem;
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
  --tw-bg-opacity: 1;
  background-color: rgb(23 93 59 / var(--tw-bg-opacity));
  padding: 0.5rem;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.message.error {
  --tw-bg-opacity: 1;
  background-color: rgb(182 37 37 / var(--tw-bg-opacity));
}

.message.success {
  --tw-bg-opacity: 1;
  background-color: rgb(0 70 126 / var(--tw-bg-opacity));
}

.message.info,
    .message.warning,
    .message.notice {
  --tw-bg-opacity: 1;
  background-color: rgb(255 224 161 / var(--tw-bg-opacity));
}

.message a {
  -webkit-text-decoration-line: underline;
          text-decoration-line: underline;
}

.product-item .price-container {
  display: block;
}

[data-content-type$='block'] .product-item .price-container p:last-child {
  margin-bottom: 1rem;
  margin-top: 0px;
}

.category-view .sidebar .product-item .price-container.filter {
}

.product-item .price-container .price {
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 600;
}

.product-item .price-container .price-label {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.product-item .special-price .price-container .price-label {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.product-item .old-price .price-container {
  --tw-text-opacity: 1;
  color: rgb(*********** / var(--tw-text-opacity));
}

.product-item .old-price .price-container .price {
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 400;
}

.page-product-bundle .price-final_price .price-from .price-container, .page-product-bundle .price-final_price .price-to .price-container {
  margin-bottom: 1rem;
  display: block;
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}

[data-content-type$='block'] .page-product-bundle .price-final_price .price-from .price-container p:last-child,[data-content-type$='block']  .page-product-bundle .price-final_price .price-to .price-container p:last-child {
  margin-bottom: 1rem;
  margin-top: 0px;
}

.category-view .sidebar .page-product-bundle .price-final_price .price-from .price-container.filter,.category-view .sidebar  .page-product-bundle .price-final_price .price-to .price-container.filter {
}

.page-product-bundle .price-final_price .price-from .price-container .price-label, .page-product-bundle .price-final_price .price-to .price-container .price-label {
  display: block;
  font-size: 1.125rem;
  line-height: 1.75rem;
  font-weight: 500;
}

[data-content-type$='block'] .page-product-bundle .price-final_price .price-from .price-container .price-label p:last-child,[data-content-type$='block']  .page-product-bundle .price-final_price .price-to .price-container .price-label p:last-child {
  margin-bottom: 1rem;
  margin-top: 0px;
}

.category-view .sidebar .page-product-bundle .price-final_price .price-from .price-container .price-label.filter,.category-view .sidebar  .page-product-bundle .price-final_price .price-to .price-container .price-label.filter {
}

.page-product-bundle .price-final_price .price-from .price-container .price, .page-product-bundle .price-final_price .price-to .price-container .price {
  display: block;
  font-size: 1.5rem;
  line-height: 2rem;
  font-weight: 600;
  line-height: 1.25;
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity));
}

[data-content-type$='block'] .page-product-bundle .price-final_price .price-from .price-container .price p:last-child,[data-content-type$='block']  .page-product-bundle .price-final_price .price-to .price-container .price p:last-child {
  margin-bottom: 1rem;
  margin-top: 0px;
}

.category-view .sidebar .page-product-bundle .price-final_price .price-from .price-container .price.filter,.category-view .sidebar  .page-product-bundle .price-final_price .price-to .price-container .price.filter {
}

.page-product-bundle .price-final_price .price-from .price-including-tax + .price-excluding-tax, .page-product-bundle .price-final_price .price-to .price-including-tax + .price-excluding-tax {
  margin-top: 0.25rem;
}

.page-product-bundle .price-final_price .price-from .price-including-tax + .price-excluding-tax .price, .page-product-bundle .price-final_price .price-to .price-including-tax + .price-excluding-tax .price {
  font-size: 1rem;
  line-height: 1.5rem;
}

.page-product-bundle .price-final_price .price-from .old-price .price-container .price,
                    .page-product-bundle .price-final_price .price-from .old-price .price-container .price-label,
                    .page-product-bundle .price-final_price .price-to .old-price .price-container .price,
                    .page-product-bundle .price-final_price .price-to .old-price .price-container .price-label {
  display: inline;
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 400;
  --tw-text-opacity: 1;
  color: rgb(*********** / var(--tw-text-opacity));
}

.super_attribute .option_label {
  font-weight: 600;
}

.super_attribute .option_price {
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(*********** / var(--tw-text-opacity));
}

.wishlist-widget .price-box .price-label {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.wishlist-widget .price-box .old-price {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.modal {
  /*
   * TODO: add tailwind classes used for the cart and modal styles.
   * This will make the modal and off-canvas styles theme specific and more adjustable.
   */
}

.backdrop {
  position: fixed;
  top: 0px;
  right: 0px;
  bottom: 0px;
  left: 0px;
  display: flex;
  background-color: rgb(0 0 0 / 0.25);
}

.snap {
  -ms-scroll-snap-type: x mandatory;
  scroll-snap-type: x mandatory;
  -ms-overflow-style: none;
  scroll-behavior: smooth;
  scrollbar-width: none;
}

.snap::-webkit-scrollbar {
  display: none;
}

.snap > div {
  scroll-snap-align: start;
}

.glider-track {
  display: flex;
}

body {
  overflow-y: scroll;
}

.clearfix::after {
  content: "";
  display: block;
  clear: both;
}

.page-main {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}

@media (min-width: 1024px) {
  .page-main {
    margin-top: 2rem;
    margin-bottom: 2rem;
  }
}

.columns {
  margin-left: auto;
  margin-right: auto;
  display: grid;
  max-width: 1770px;
  grid-template-columns: repeat(1, minmax(0, 1fr));
  column-gap: 2rem;
  row-gap: 1rem;
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 1rem;
}

@media (min-width: 768px) {
  .columns {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

.columns {
  grid-template-rows: auto minmax(0, 1fr);
}

.columns .main {
  order: 2;
}

.columns .sidebar {
  order: 3;
}

.product-main-full-width .columns {
  max-width: none;
}

.page-main-full-width .columns {
  max-width: none;
  padding-left: 0px;
  padding-right: 0px;
}

.page-with-filter .columns .sidebar-main {
  order: 1;
}

@media (min-width: 640px) {
  .page-layout-2columns-left .columns, .page-layout-2columns-right .columns, .page-layout-3columns .columns {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .page-layout-2columns-left .columns .main, .page-layout-2columns-right .columns .main, .page-layout-3columns .columns .main {
    grid-column: span 2 / span 2;
  }

  .page-layout-2columns-left .columns .sidebar, .page-layout-2columns-right .columns .sidebar, .page-layout-3columns .columns .sidebar {
    order: 3;
  }

  .page-with-filter .columns .sidebar-main {
    order: 1;
    grid-column: span 2 / span 2;
  }
}

@media (min-width: 768px) {
  .page-layout-2columns-left .columns, .page-layout-2columns-right .columns, .page-layout-3columns .columns {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .page-layout-2columns-left .columns .main, .page-layout-2columns-right .columns .main, .page-layout-3columns .columns .main {
    grid-row: span 2 / span 2;
  }

  .page-layout-2columns-left .columns .sidebar, .page-layout-2columns-right .columns .sidebar, .page-layout-3columns .columns .sidebar {
    grid-column: span 1 / span 1;
  }

  .page-layout-2columns-left .columns .main, .page-layout-3columns .columns .main {
    grid-column-start: 2 !important;
  }

  .page-layout-2columns-left .columns .sidebar, .page-layout-3columns .columns .sidebar {
    order: 1;
  }

  .page-layout-2columns-left .columns .sidebar ~ .sidebar-additional, .page-layout-3columns .columns .sidebar ~ .sidebar-additional {
    order: 3;
  }

  .page-layout-2columns-right .sidebar-main, .page-layout-2columns-right.page-with-filter .sidebar-main {
    order: 3;
  }
}

@media (min-width: 1024px) {
  .page-layout-2columns-left .columns, .page-layout-2columns-right .columns, .page-layout-3columns .columns {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .page-layout-2columns-left .columns .main, .page-layout-2columns-right .columns .main {
    grid-column: span 3 / span 3;
  }

  .page-layout-3columns .columns .sidebar-additional {
    grid-column-start: 4;
  }
}

.product-image-container {
  width: 100% !important;
}

.product-image-container img {
  width: 100%;
}

.swatch-attribute .swatch-attribute-options {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}

.swatch-attribute .swatch-attribute-options .swatch-option {
  margin: 0.25rem;
  display: flex;
  justify-content: center;
  border-width: 1px;
  padding-left: 0.5rem;
  padding-right: 0.5rem;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  min-width: 40px;
}

body {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

#cart-drawer {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}

[x-cloak] {
  display: none !important;
}

.input {
  margin-right: 0.5rem;
  width: auto;
  border-radius: 0.25rem;
  border-width: 1px;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 1rem;
  padding-right: 1rem;
  font-size: 1rem;
  line-height: 1.5rem;
}

@media (min-width: 768px) {
  .input {
    margin-right: 1rem;
  }
}

@media (min-width: 1024px) {
  .input {
    margin-right: 0px;
  }
}

@media (min-width: 1280px) {
  .input {
    margin-right: 1rem;
  }
}

.input-light {
  --tw-border-opacity: 1;
  border-color: rgb(156 163 175 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.input-light:focus {
  --tw-border-opacity: 1;
  border-color: rgb(66 66 65 / var(--tw-border-opacity));
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.card {
  border-radius: 0.125rem;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  padding: 1rem;
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.card-interactive:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(250 250 250 / var(--tw-bg-opacity));
}

.duration-200 {
  transition-duration: 200ms;
}

.ease-in {
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}

.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

.transition {
  transition: transform 250ms ease, color 250ms ease;
}

.transform-180 {
  transform: rotate(-180deg);
}

.univers {
  font: 400 1.2rem/2.2rem 'Univers';
}

html {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: Univers, ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-size: 1rem;
  line-height: 1.5rem;
  --tw-text-opacity: 1;
  color: rgb(0 70 126 / var(--tw-text-opacity));
}

/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

/**
 * Layout
 */

.page-main-full-width .columns [data-content-type='row'][data-appearance='contained'],[data-content-type='row'][data-appearance='contained'] {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 1.5rem;
  padding-left: 1.5rem;
}

@media (min-width: 640px) {
  .page-main-full-width .columns [data-content-type='row'][data-appearance='contained'],[data-content-type='row'][data-appearance='contained'] {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .page-main-full-width .columns [data-content-type='row'][data-appearance='contained'],[data-content-type='row'][data-appearance='contained'] {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .page-main-full-width .columns [data-content-type='row'][data-appearance='contained'],[data-content-type='row'][data-appearance='contained'] {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .page-main-full-width .columns [data-content-type='row'][data-appearance='contained'],[data-content-type='row'][data-appearance='contained'] {
    max-width: 1280px;
  }
}

@media (min-width: 1770px) {
  .page-main-full-width .columns [data-content-type='row'][data-appearance='contained'],[data-content-type='row'][data-appearance='contained'] {
    max-width: 1770px;
  }
}

.page-main-full-width .columns [data-content-type='row'][data-appearance='contained'],[data-content-type='row'][data-appearance='contained'] {
  padding: 0px;
}

@media (min-width: 1024px) {
  .page-main-full-width .columns [data-content-type='row'][data-appearance='contained'],[data-content-type='row'][data-appearance='contained'] {
    padding: 0.375rem;
  }
}

@media (min-width: 1280px) {
  .page-main-full-width .columns [data-content-type='row'][data-appearance='contained'],[data-content-type='row'][data-appearance='contained'] {
    max-width: 1280px;
  }
}

/* Rows */

[data-content-type='row'] {
  box-sizing: border-box;
}

@media (min-width: 768px) {
  [data-content-type='row'] [data-appearance='carousel'] [data-content-type='row'] > div, [data-content-type='row'] [data-appearance='slider'] [data-content-type='row'] > div {
    padding: 0px;
  }
}

[data-content-type='row'] > div {
  margin-bottom: 0.625rem;
  padding: 0px;
}

@media (min-width: 768px) {
  [data-content-type='row'] > div {
    padding: 0.625rem;
  }
}

[data-content-type='row'][data-appearance='contained'] {
  margin-left: auto;
  margin-right: auto;
  box-sizing: border-box;
}

[data-content-type='row'][data-appearance='contained'] [data-element='inner'] {
  box-sizing: border-box;
  background-attachment: scroll !important;
}

[data-content-type='row'][data-appearance='full-bleed'] {
  background-attachment: scroll !important;
}

[data-content-type='row'][data-appearance='full-width'] {
  background-attachment: scroll !important;
}

[data-content-type='row'][data-appearance='full-width'] > .row-full-width-inner {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 1.5rem;
  padding-left: 1.5rem;
}

@media (min-width: 640px) {
  [data-content-type='row'][data-appearance='full-width'] > .row-full-width-inner {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  [data-content-type='row'][data-appearance='full-width'] > .row-full-width-inner {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  [data-content-type='row'][data-appearance='full-width'] > .row-full-width-inner {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  [data-content-type='row'][data-appearance='full-width'] > .row-full-width-inner {
    max-width: 1280px;
  }
}

@media (min-width: 1770px) {
  [data-content-type='row'][data-appearance='full-width'] > .row-full-width-inner {
    max-width: 1770px;
  }
}

/* Column Groups */

[data-content-type='column-group'], [data-content-type='column-line'] {
  flex-wrap: wrap;
}

@media (min-width: 768px) {
  [data-content-type='column-group'], [data-content-type='column-line'] {
    flex-wrap: nowrap;
    gap: 2rem;
  }

  .nogap [data-content-type='column-group'], .nogap [data-content-type='column-line'] {
    gap: 0px;
  }
}

/* Columns */

[data-content-type='column'] {
  box-sizing: border-box;
  width: 100%;
  max-width: 100%;
  background-attachment: scroll !important;
  flex-basis: 100%;
}

@media (min-width: 768px) {
  [data-content-type='column'] {
    flex-basis: auto
  }
}

/* Tabs/Tab Item */

[data-content-type='tabs'] .tabs-navigation {
  margin-bottom: -1px;
  display: block;
  padding: 0px;
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

[data-content-type$='block'] [data-content-type='tabs'] .tabs-navigation p:last-child {
  margin-bottom: 1rem;
  margin-top: 0px;
}

.category-view .sidebar [data-content-type='tabs'] .tabs-navigation.filter {
}

[data-content-type='tabs'] .tabs-navigation li.tab-header {
  position: relative;
  margin-top: 0px;
  margin-bottom: 0px;
  margin-right: 0px;
  margin-left: -1px;
  display: inline-block;
  max-width: 100%;
  overflow-wrap: break-word;
  border-bottom-left-radius: 0px;
  border-bottom-right-radius: 0px;
  border-width: 1px;
  border-bottom-width: 0px;
  border-style: solid;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}

[data-content-type='tabs'] .tabs-navigation li.tab-header:first-child {
  margin-left: 0px;
}

[data-content-type='tabs'] .tabs-navigation li.tab-header.active {
  z-index: 20;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  border-bottom: 1px solid white;
}

[data-content-type='tabs'] .tabs-navigation li.tab-header a.tab-title {
  position: relative;
  display: block;
  cursor: pointer;
  white-space: normal;
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  padding-left: 1.25rem;
  padding-right: 1.25rem;
  vertical-align: middle;
  font-size: 0.875rem;
  line-height: 1.25rem;
  font-weight: 600;
}

[data-content-type$='block'] [data-content-type='tabs'] .tabs-navigation li.tab-header a.tab-title p:last-child {
  margin-bottom: 1rem;
  margin-top: 0px;
}

.category-view .sidebar [data-content-type='tabs'] .tabs-navigation li.tab-header a.tab-title.filter {
}

[data-content-type='tabs'] .tabs-navigation li.tab-header a.tab-title span {
  font-weight: 600;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}

[data-content-type='tabs'] .tabs-content {
  position: relative;
  z-index: 10;
  box-sizing: border-box;
  overflow: hidden;
  border-radius: 0.125rem;
  border-width: 1px;
  border-style: solid;
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

[data-content-type='tabs'] .tabs-content [data-content-type='tab-item'] {
  box-sizing: border-box;
  padding: 2rem;
  min-height: inherit;
  background-attachment: scroll !important;
}

[data-content-type='tabs'].tab-align-left .tabs-content {
  border-top-left-radius: 0 !important;
}

[data-content-type='tabs'].tab-align-right .tabs-content {
  border-top-right-radius: 0 !important;
}

/**
 * Elements
 */

/* Text */

[data-content-type='text'] {
  overflow-wrap: break-word;
}

/* Heading */

[data-content-type='heading'] {
  overflow-wrap: break-word;
}

/* Buttons/Button Item */

[data-content-type='buttons'] {
  max-width: 100%;
}

[data-content-type='buttons'] [data-content-type='button-item'] {
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
  max-width: 100%;
}

[data-content-type='buttons'] [data-content-type='button-item'] [data-element='link'],
    [data-content-type='buttons'] [data-content-type='button-item'] [data-element='empty_link'] {
  max-width: 100%;
  overflow-wrap: break-word;
}

[data-content-type='buttons'] [data-content-type='button-item'] [data-element='empty_link'] {
  cursor: default;
}

[data-content-type='buttons'] [data-content-type='button-item'] a,
    [data-content-type='buttons'] [data-content-type='button-item'] button,
    [data-content-type='buttons'] [data-content-type='button-item'] div {
  display: inline-block;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

[data-content-type='buttons'] [data-content-type='button-item'] a.pagebuilder-button-link, [data-content-type='buttons'] [data-content-type='button-item'] button.pagebuilder-button-link, [data-content-type='buttons'] [data-content-type='button-item'] div.pagebuilder-button-link {
  box-sizing: border-box;
}

a.pagebuilder-button-primary, button.pagebuilder-button-primary, div.pagebuilder-button-primary {
  display: flex;
  align-items: center;
  --tw-bg-opacity: 1;
  background-color: rgb(42 42 41 / var(--tw-bg-opacity));
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 400;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

@media (min-width: 768px) {
  a.pagebuilder-button-primary, button.pagebuilder-button-primary, div.pagebuilder-button-primary {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
}

@media (min-width: 1280px) {
  a.pagebuilder-button-primary, button.pagebuilder-button-primary, div.pagebuilder-button-primary {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}

a.pagebuilder-button-primary svg, button.pagebuilder-button-primary svg, div.pagebuilder-button-primary svg {
  display: inline-flex;
}

a.pagebuilder-button-primary span, button.pagebuilder-button-primary span, div.pagebuilder-button-primary span {
  vertical-align: middle;
}

a.pagebuilder-button-primary:hover, button.pagebuilder-button-primary:hover, div.pagebuilder-button-primary:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(66 66 65 / var(--tw-bg-opacity));
}

a.pagebuilder-button-primary:focus, button.pagebuilder-button-primary:focus, div.pagebuilder-button-primary:focus {
  border-color: transparent;
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-color: rgb(0 70 126 / var(--tw-ring-opacity));
  --tw-ring-opacity: 0.5;
}

a.pagebuilder-button-primary, button.pagebuilder-button-primary, div.pagebuilder-button-primary {
  --tw-bg-opacity: 1;
  background-color: rgb(42 42 41 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

a.pagebuilder-button-primary:hover, button.pagebuilder-button-primary:hover, div.pagebuilder-button-primary:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(66 66 65 / var(--tw-bg-opacity));
}

a.pagebuilder-button-secondary, button.pagebuilder-button-secondary, div.pagebuilder-button-secondary {
  display: flex;
  align-items: center;
  --tw-bg-opacity: 1;
  background-color: rgb(42 42 41 / var(--tw-bg-opacity));
  padding-left: 1rem;
  padding-right: 1rem;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-size: 1rem;
  line-height: 1.5rem;
  font-weight: 400;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

@media (min-width: 768px) {
  a.pagebuilder-button-secondary, button.pagebuilder-button-secondary, div.pagebuilder-button-secondary {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
}

@media (min-width: 1280px) {
  a.pagebuilder-button-secondary, button.pagebuilder-button-secondary, div.pagebuilder-button-secondary {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}

a.pagebuilder-button-secondary svg, button.pagebuilder-button-secondary svg, div.pagebuilder-button-secondary svg {
  display: inline-flex;
}

a.pagebuilder-button-secondary span, button.pagebuilder-button-secondary span, div.pagebuilder-button-secondary span {
  vertical-align: middle;
}

a.pagebuilder-button-secondary:hover, button.pagebuilder-button-secondary:hover, div.pagebuilder-button-secondary:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(66 66 65 / var(--tw-bg-opacity));
}

a.pagebuilder-button-secondary:focus, button.pagebuilder-button-secondary:focus, div.pagebuilder-button-secondary:focus {
  border-color: transparent;
  outline: 2px solid transparent;
  outline-offset: 2px;
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
  --tw-ring-color: rgb(0 70 126 / var(--tw-ring-opacity));
  --tw-ring-opacity: 0.5;
}

a.pagebuilder-button-secondary, button.pagebuilder-button-secondary, div.pagebuilder-button-secondary {
  border-width: 2px;
  --tw-border-opacity: 1;
  border-color: rgb(*********** / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

a.pagebuilder-button-secondary:hover, button.pagebuilder-button-secondary:hover, div.pagebuilder-button-secondary:hover {
  --tw-border-opacity: 1;
  border-color: rgb(42 42 41 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

a.pagebuilder-button-secondary:focus, button.pagebuilder-button-secondary:focus, div.pagebuilder-button-secondary:focus {
  border-width: 2px;
  border-color: transparent;
}

/* HTML Code */

[data-content-type='html'] {
  overflow-wrap: break-word;
}

/**
 * Media
 */

/* Image */

[data-content-type='image'] {
  box-sizing: border-box;
}

[data-content-type='image'] > [data-element='link'],
  [data-content-type='image'] > [data-element='link'] img {
  border-radius: inherit;
}

[data-content-type='image'] .pagebuilder-mobile-hidden {
  display: none;
}

@media (min-width: 768px) {
  [data-content-type='image'] .pagebuilder-mobile-hidden {
    display: block;
  }

  [data-content-type='image'] .pagebuilder-mobile-only {
    display: none;
  }
}

[data-content-type='image'] figcaption {
  overflow-wrap: break-word;
}

/* Video */

[data-content-type='video'] {
  font-size: 0;
}

[data-content-type='video'] .pagebuilder-video-inner {
  box-sizing: border-box;
  display: inline-block;
  width: 100%;
}

[data-content-type='video'] .pagebuilder-video-container {
  position: relative;
  overflow: hidden;
  border-radius: inherit;
  padding-top: 56.25%;
}

[data-content-type='video'] iframe,
  [data-content-type='video'] video {
  position: absolute;
  left: 0px;
  top: 0px;
  height: 100%;
  width: 100%;
}

/* Banner */

[data-content-type='banner'] > [data-element='link'], [data-content-type='banner'] > [data-element='empty_link'] {
  color: inherit;
  text-decoration: inherit;
}

[data-content-type='banner'] > [data-element='link']:hover, [data-content-type='banner'] > [data-element='empty_link']:hover {
  color: inherit;
  text-decoration: inherit;
}

[data-content-type='banner'] .pagebuilder-banner-wrapper {
  box-sizing: border-box;
  overflow-wrap: break-word;
  background-clip: padding-box;
  border-radius: inherit;
  background-attachment: scroll !important;
}

[data-content-type='banner'] .pagebuilder-banner-wrapper .pagebuilder-overlay {
  position: relative;
  box-sizing: border-box;
  padding: 2rem;
  transition-property: color, background-color, border-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-text-decoration-color, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-text-decoration-color, -webkit-backdrop-filter;
  transition-duration: 500ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition: transform 250ms ease, color 250ms ease;
}

[data-content-type='banner'] .pagebuilder-banner-wrapper .pagebuilder-overlay.pagebuilder-poster-overlay {
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

[data-content-type='banner'] .pagebuilder-banner-wrapper .pagebuilder-overlay:not(.pagebuilder-poster-overlay) {
  max-width: none;
}

@media (min-width: 768px) {
  [data-content-type='banner'] .pagebuilder-banner-wrapper .pagebuilder-overlay:not(.pagebuilder-poster-overlay) {
    max-width: 36rem;
  }
}

[data-content-type='banner'] .pagebuilder-banner-wrapper.jarallax .video-overlay {
  z-index: 0;
}

[data-content-type='banner'] .pagebuilder-banner-wrapper [data-element='content'] {
  min-height: 50px;
}

[data-content-type='banner'] .pagebuilder-banner-wrapper .pagebuilder-banner-button {
  margin: 0px;
  margin-top: 1.25rem;
  display: inline-block;
  max-width: 100%;
  overflow-wrap: break-word;
  transition-property: opacity;
  transition-duration: 500ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  text-align: inherit;
}

[data-content-type='banner'] .pagebuilder-banner-wrapper .pagebuilder-poster-content {
  width: 100%;
}

[data-content-type='banner'][data-appearance='collage-centered'] .pagebuilder-banner-wrapper .pagebuilder-overlay {
  margin-left: auto;
  margin-right: auto;
}

[data-content-type='banner'][data-appearance='collage-left'] .pagebuilder-banner-wrapper .pagebuilder-overlay {
  margin-right: auto;
}

[data-content-type='banner'][data-appearance='collage-right'] .pagebuilder-banner-wrapper .pagebuilder-overlay {
  margin-left: auto;
}

/* Slider */

[data-content-type='slider'] {
  visibility: hidden;
  position: relative;
  overflow: hidden;
}

[data-content-type='slider'].swiper-initialized {
  visibility: visible;
}

[data-content-type='slider'].swiper-initialized .swiper-pagination{
  bottom: 0;
}

[data-content-type='slider'] a.button {
  color: initial;
  padding: 10px;
  text-decoration: none;
}

[data-content-type='slider'] .carousel-nav {
  position: absolute;
  bottom: 0px;
  margin-bottom: 0.5rem;
  background-color: rgb(249 250 251 / 0.75);
  left: 50%;
  transform: translateX(-50%);
}

[data-role='glider-content'] {
  overflow: hidden;
}

/* Slide */

[data-content-type='slide'] {
  box-sizing: border-box;
  overflow: hidden;
  line-height: 1.25rem;
  min-height: inherit;
}

[data-content-type='slide'] > [data-element='link'],
  [data-content-type='slide'] > [data-element='empty_link'] {
  color: inherit;
  min-height: inherit;
  text-decoration: inherit;
}

[data-content-type='slide'] > [data-element='link']:hover, [data-content-type='slide'] > [data-element='empty_link']:hover {
  color: inherit;
  text-decoration: inherit;
}

[data-content-type='slide'] .pagebuilder-slide-wrapper {
  box-sizing: border-box;
  overflow-wrap: break-word;
  border-radius: inherit;
  min-height: inherit;
}

[data-content-type='slide'] .pagebuilder-slide-wrapper .jarallax-viewport-element {
  position: absolute;
  top: 0px;
  z-index: 50;
  height: 100%;
  width: 0.125rem;
  left: -15000vw;
}

[data-content-type='slide'] .pagebuilder-slide-wrapper.jarallax .video-overlay {
  -webkit-transform: unset;
  z-index: 1;
}

[data-content-type='slide'] .pagebuilder-slide-wrapper.jarallax .pagebuilder-overlay {
  position: relative;
  z-index: 20;
}

[data-content-type='slide'] .pagebuilder-slide-wrapper.jarallax [id*='jarallax-container'] > div,
        [data-content-type='slide'] .pagebuilder-slide-wrapper.jarallax [id*='jarallax-container'] > img,
        [data-content-type='slide'] .pagebuilder-slide-wrapper.jarallax [id*='jarallax-container'] > video,
        [data-content-type='slide'] .pagebuilder-slide-wrapper.jarallax [id*='jarallax-container'] > iframe {
  margin: auto !important;
  transform: none !important;
}

[data-content-type='slide'] .pagebuilder-slide-wrapper .pagebuilder-overlay {
  box-sizing: border-box;
  padding: 2rem;
  transition-property: color, background-color, border-color, fill, stroke, -webkit-text-decoration-color;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, -webkit-text-decoration-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  border-radius: inherit;
}

[data-content-type='slide'] .pagebuilder-slide-wrapper .pagebuilder-overlay.pagebuilder-poster-overlay {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: inherit;
}

[data-content-type='slide'] .pagebuilder-slide-wrapper .pagebuilder-overlay:not(.pagebuilder-poster-overlay) {
  max-width: none;
}

@media (min-width: 768px) {
  [data-content-type='slide'] .pagebuilder-slide-wrapper .pagebuilder-overlay:not(.pagebuilder-poster-overlay) {
    max-width: 32rem;
  }
}

[data-content-type='slide'] .pagebuilder-slide-wrapper [data-element='content'] {
  overflow: auto;
  min-height: 50px;
}

[data-content-type='slide'] .pagebuilder-slide-wrapper .pagebuilder-slide-button {
  margin: 0px;
  margin-top: 1.25rem;
  max-width: 100%;
  overflow-wrap: break-word;
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  text-align: inherit;
}

[data-content-type='slide'] .pagebuilder-slide-wrapper .pagebuilder-poster-content {
  width: 100%;
}

[data-content-type='slide'][data-appearance='collage-centered'] .pagebuilder-slide-wrapper .pagebuilder-overlay {
  margin-left: auto;
  margin-right: auto;
}

[data-content-type='slide'][data-appearance='collage-left'] .pagebuilder-slide-wrapper .pagebuilder-overlay {
  margin-right: auto;
}

[data-content-type='slide'][data-appearance='collage-right'] .pagebuilder-slide-wrapper .pagebuilder-overlay {
  margin-left: auto;
}

/* Map */

[data-content-type='map'] {
  box-sizing: border-box;
  height: 18rem;
}

/**
 * Add Content
 */

/* Block */

[data-content-type$='block'] .block p:last-child {
  margin-bottom: 1rem;
  margin-top: 0px;
}

/* Dynamic Block */

[data-content-type='dynamic_block'] [data-content-type='image'] img {
  display: inline;
}

[data-content-type='dynamic_block'] .block-banners .banner-item-content,
  [data-content-type='dynamic_block'] .block-banners-inline .banner-item-content {
  margin-bottom: auto;
}

.swiper-button-next.swiper-no-icon, .swiper-button-prev.swiper-no-icon{
  width: 2.5rem;
  height: 2.5rem;
  color:#fff;
}

.swiper-no-icon:after {
  display: none;
}

[data-content-type='row'] > div {
  padding: 0;
}

/* Product Carousel Styling */

[data-content-type='products'][data-appearance='carousel'] {
  position: relative;
}

.glider-track {
  position: relative;
}

/* Partial height borders between product tiles */

.glider-track .glider-slide:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 50%;
  right: 0px;
  z-index: 10;
  width: 1px;
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
  height: 70%;
  /* Partial height - 70% of tile height */
}

.glider-track .glider-slide:hover {
  z-index: 20;
}

/* Hide border on hover to prevent visual conflicts */

.glider-track .glider-slide:hover::after,
.glider-track .glider-slide:hover + .glider-slide::after {
  opacity: 0;
}

.reviews-star-filled {
  stop-color: #e8b723;
}

.reviews-star-empty {
  stop-color: #cbd5e0;
}

[data-content-type="accordion-item"] {
  border-top-width: 1px;
}

[data-content-type="accordion"] [data-collapsible="true"]:after{
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2.5" stroke="%23404542" class="w-6 h-6"> <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" /> </svg>');
}

[data-content-type="accordion"] [data-collapsible="true"]:hover:after{
  transform: translateX(-0.25rem);
}

[data-content-type="accordion"] [aria-expanded="true"] [data-collapsible="true"]:after {
  transform: rotate(-180deg);
}

/* purgecss end ignore */

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

.pointer-events-none {
  pointer-events: none;
}

.visible {
  visibility: visible;
}

.invisible {
  visibility: hidden;
}

.static {
  position: static;
}

.fixed {
  position: fixed;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.sticky {
  position: -webkit-sticky;
  position: sticky;
}

.inset-0 {
  top: 0px;
  right: 0px;
  bottom: 0px;
  left: 0px;
}

.inset-4 {
  top: 1rem;
  right: 1rem;
  bottom: 1rem;
  left: 1rem;
}

.inset-y-0 {
  top: 0px;
  bottom: 0px;
}

.inset-x-4 {
  left: 1rem;
  right: 1rem;
}

.inset-x-0 {
  left: 0px;
  right: 0px;
}

.top-1\/2 {
  top: 50%;
}

.left-0 {
  left: 0px;
}

.right-0 {
  right: 0px;
}

.top-0 {
  top: 0px;
}

.-top-1 {
  top: -0.25rem;
}

.-right-1 {
  right: -0.25rem;
}

.top-3 {
  top: 0.75rem;
}

.left-3 {
  left: 0.75rem;
}

.right-3 {
  right: 0.75rem;
}

.bottom-0 {
  bottom: 0px;
}

.top-full {
  top: 100%;
}

.left-1\/2 {
  left: 50%;
}

.top-6 {
  top: 1.5rem;
}

.right-6 {
  right: 1.5rem;
}

.right-20 {
  right: 5rem;
}

.bottom-4 {
  bottom: 1rem;
}

.right-12 {
  right: 3rem;
}

.top-12 {
  top: 3rem;
}

.right-2 {
  right: 0.5rem;
}

.-top-1\.5 {
  top: -0.375rem;
}

.-right-1\.5 {
  right: -0.375rem;
}

.isolate {
  isolation: isolate;
}

.z-40 {
  z-index: 40;
}

.z-50 {
  z-index: 50;
}

.z-30 {
  z-index: 30;
}

.z-10 {
  z-index: 10;
}

.z-20 {
  z-index: 20;
}

.z-0 {
  z-index: 0;
}

.order-first {
  order: -9999;
}

.order-1 {
  order: 1;
}

.order-2 {
  order: 2;
}

.order-3 {
  order: 3;
}

.order-last {
  order: 9999;
}

.col-span-3 {
  grid-column: span 3 / span 3;
}

.col-span-2 {
  grid-column: span 2 / span 2;
}

.col-span-1 {
  grid-column: span 1 / span 1;
}

.col-span-full {
  grid-column: 1 / -1;
}

.col-span-4 {
  grid-column: span 4 / span 4;
}

.float-left {
  float: left;
}

.clear-left {
  clear: left;
}

.m-auto {
  margin: auto;
}

.-m-2 {
  margin: -0.5rem;
}

.m-0 {
  margin: 0px;
}

.-m-3 {
  margin: -0.75rem;
}

.m-4 {
  margin: 1rem;
}

.m-2 {
  margin: 0.5rem;
}

.m-1 {
  margin: 0.25rem;
}

.-m-4 {
  margin: -1rem;
}

.mx-1 {
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.my-2 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.my-1 {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}

.my-3 {
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
}

.my-8 {
  margin-top: 2rem;
  margin-bottom: 2rem;
}

.mx-0 {
  margin-left: 0px;
  margin-right: 0px;
}

.-mx-4 {
  margin-left: -1rem;
  margin-right: -1rem;
}

.mx-1\.5 {
  margin-left: 0.375rem;
  margin-right: 0.375rem;
}

.my-6 {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}

.mx-4 {
  margin-left: 1rem;
  margin-right: 1rem;
}

.my-10 {
  margin-top: 2.5rem;
  margin-bottom: 2.5rem;
}

.my-0 {
  margin-top: 0px;
  margin-bottom: 0px;
}

.-mx-2 {
  margin-left: -0.5rem;
  margin-right: -0.5rem;
}

.-mx-1 {
  margin-left: -0.25rem;
  margin-right: -0.25rem;
}

.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}

.-mx-6 {
  margin-left: -1.5rem;
  margin-right: -1.5rem;
}

.my-12 {
  margin-top: 3rem;
  margin-bottom: 3rem;
}

.-mx-3 {
  margin-left: -0.75rem;
  margin-right: -0.75rem;
}

.mx-6 {
  margin-left: 1.5rem;
  margin-right: 1.5rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mt-8 {
  margin-top: 2rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mr-4 {
  margin-right: 1rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.mt-3 {
  margin-top: 0.75rem;
}

.mr-3 {
  margin-right: 0.75rem;
}

.mt-auto {
  margin-top: auto;
}

.mr-1 {
  margin-right: 0.25rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.mb-auto {
  margin-bottom: auto;
}

.-ml-4 {
  margin-left: -1rem;
}

.mt-0 {
  margin-top: 0px;
}

.mt-4 {
  margin-top: 1rem;
}

.ml-1 {
  margin-left: 0.25rem;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.ml-6 {
  margin-left: 1.5rem;
}

.mr-5 {
  margin-right: 1.25rem;
}

.ml-0 {
  margin-left: 0px;
}

.mb-px {
  margin-bottom: 1px;
}

.-ml-1 {
  margin-left: -0.25rem;
}

.mt-5 {
  margin-top: 1.25rem;
}

.mb-12 {
  margin-bottom: 3rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.mb-8 {
  margin-bottom: 2rem;
}

.-mr-4 {
  margin-right: -1rem;
}

.mb-0 {
  margin-bottom: 0px;
}

.mt-16 {
  margin-top: 4rem;
}

.mt-12 {
  margin-top: 3rem;
}

.-mt-5 {
  margin-top: -1.25rem;
}

.-mt-4 {
  margin-top: -1rem;
}

.ml-3 {
  margin-left: 0.75rem;
}

.ml-7 {
  margin-left: 1.75rem;
}

.mr-auto {
  margin-right: auto;
}

.ml-auto {
  margin-left: auto;
}

.mr-6 {
  margin-right: 1.5rem;
}

.-mt-2 {
  margin-top: -0.5rem;
}

.mt-6 {
  margin-top: 1.5rem;
}

.-mr-1 {
  margin-right: -0.25rem;
}

.-mt-6 {
  margin-top: -1.5rem;
}

.ml-4 {
  margin-left: 1rem;
}

.mr-10 {
  margin-right: 2.5rem;
}

.mt-10 {
  margin-top: 2.5rem;
}

.mt-0\.5 {
  margin-top: 0.125rem;
}

.-ml-px {
  margin-left: -1px;
}

.mb-10 {
  margin-bottom: 2.5rem;
}

.-mt-8 {
  margin-top: -2rem;
}

.-mr-2 {
  margin-right: -0.5rem;
}

.\!ml-0 {
  margin-left: 0px !important;
}

.-ml-6 {
  margin-left: -1.5rem;
}

.mr-8 {
  margin-right: 2rem;
}

.ml-10 {
  margin-left: 2.5rem;
}

.box-border {
  box-sizing: border-box;
}

.box-content {
  box-sizing: content-box;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.inline {
  display: inline;
}

.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.table {
  display: table;
}

.table-caption {
  display: table-caption;
}

.table-cell {
  display: table-cell;
}

.table-row {
  display: table-row;
}

.grid {
  display: grid;
}

.contents {
  display: contents;
}

.hidden {
  display: none;
}

.aspect-square {
  aspect-ratio: 1 / 1;
}

.h-16 {
  height: 4rem;
}

.h-5 {
  height: 1.25rem;
}

.h-8 {
  height: 2rem;
}

.h-6 {
  height: 1.5rem;
}

.h-4 {
  height: 1rem;
}

.\!h-\[98\%\] {
  height: 98% !important;
}

.h-full {
  height: 100%;
}

.h-2 {
  height: 0.5rem;
}

.h-12 {
  height: 3rem;
}

.h-3 {
  height: 0.75rem;
}

.h-48 {
  height: 12rem;
}

.h-screen {
  height: 100vh;
}

.h-max {
  height: -webkit-max-content;
  height: -moz-max-content;
  height: max-content;
}

.h-0\.5 {
  height: 0.125rem;
}

.h-0 {
  height: 0px;
}

.h-10 {
  height: 2.5rem;
}

.h-9 {
  height: 2.25rem;
}

.h-7 {
  height: 1.75rem;
}

.h-14 {
  height: 3.5rem;
}

.h-auto {
  height: auto;
}

.h-11 {
  height: 2.75rem;
}

.max-h-screen-75 {
  max-height: 75vh;
}

.max-h-screen {
  max-height: 100vh;
}

.min-h-14 {
  min-height: 3.5rem;
}

.min-h-a11y {
  min-height: 44px;
}

.min-h-\[20px\] {
  min-height: 20px;
}

.w-16 {
  width: 4rem;
}

.w-12 {
  width: 3rem;
}

.w-5 {
  width: 1.25rem;
}

.w-full {
  width: 100%;
}

.w-8 {
  width: 2rem;
}

.w-6 {
  width: 1.5rem;
}

.w-auto {
  width: auto;
}

.w-4 {
  width: 1rem;
}

.w-2 {
  width: 0.5rem;
}

.w-screen {
  width: 100vw;
}

.w-10 {
  width: 2.5rem;
}

.w-32 {
  width: 8rem;
}

.w-3 {
  width: 0.75rem;
}

.w-max {
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
}

.w-40 {
  width: 10rem;
}

.w-1\/2 {
  width: 50%;
}

.w-44 {
  width: 11rem;
}

.w-60 {
  width: 15rem;
}

.w-9 {
  width: 2.25rem;
}

.w-7 {
  width: 1.75rem;
}

.w-14 {
  width: 3.5rem;
}

.w-20 {
  width: 5rem;
}

.w-7\/12 {
  width: 58.333333%;
}

.w-5\/12 {
  width: 41.666667%;
}

.w-1\/3 {
  width: 33.333333%;
}

.w-2\/3 {
  width: 66.666667%;
}

.w-24 {
  width: 6rem;
}

.w-fit {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.w-64 {
  width: 16rem;
}

.w-0 {
  width: 0px;
}

.w-4\/12 {
  width: 33.333333%;
}

.w-8\/12 {
  width: 66.666667%;
}

.w-56 {
  width: 14rem;
}

.w-1\/4 {
  width: 25%;
}

.w-3\/4 {
  width: 75%;
}

.w-11 {
  width: 2.75rem;
}

.min-w-48 {
  min-width: 12rem;
}

.min-w-40 {
  min-width: 10rem;
}

.min-w-20 {
  min-width: 5rem;
}

.max-w-screen-2xl {
  max-width: 1770px;
}

.max-w-screen-xl {
  max-width: 1280px;
}

.max-w-screen-lg {
  max-width: 1024px;
}

.max-w-xs {
  max-width: 20rem;
}

.max-w-xl {
  max-width: 36rem;
}

.max-w-full {
  max-width: 100%;
}

.max-w-prose {
  max-width: 65ch;
}

.max-w-4xl {
  max-width: 56rem;
}

.max-w-md {
  max-width: 28rem;
}

.flex-1 {
  flex: 1 1 0%;
}

.flex-none {
  flex: none;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

.shrink-0 {
  flex-shrink: 0;
}

.shrink {
  flex-shrink: 1;
}

.grow-0 {
  flex-grow: 0;
}

.grow {
  flex-grow: 1;
}

.table-auto {
  table-layout: auto;
}

.border-collapse {
  border-collapse: collapse;
}

.origin-top-right {
  transform-origin: top right;
}

.origin-top-left {
  transform-origin: top left;
}

.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-x-full {
  --tw-translate-x: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-0 {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-full {
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-x-1 {
  --tw-translate-x: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-1\/2 {
  --tw-translate-y: 50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-full {
  --tw-translate-y: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-y-8 {
  --tw-translate-y: -2rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-full {
  --tw-translate-y: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-0 {
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.-translate-x-5 {
  --tw-translate-x: -1.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-4 {
  --tw-translate-x: 1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-45 {
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@keyframes spin-reverse {
  from {
    transform: rotate(360deg);
  }
}

.animate-spin-reverse {
  animation: spin-reverse 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

.cursor-pointer {
  cursor: pointer;
}

.cursor-default {
  cursor: default;
}

.select-none {
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

.resize {
  resize: both;
}

.list-disc {
  list-style-type: disc;
}

.appearance-none {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}

.grid-flow-row {
  grid-auto-flow: row;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-7 {
  grid-template-columns: repeat(7, minmax(0, 1fr));
}

.grid-cols-5 {
  grid-template-columns: repeat(5, minmax(0, 1fr));
}

.flex-row {
  flex-direction: row;
}

.flex-row-reverse {
  flex-direction: row-reverse;
}

.flex-col {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

.place-content-between {
  place-content: space-between;
}

.place-items-center {
  place-items: center;
}

.content-center {
  align-content: center;
}

.content-start {
  align-content: flex-start;
}

.content-end {
  align-content: flex-end;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.items-center {
  align-items: center;
}

.items-baseline {
  align-items: baseline;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-evenly {
  justify-content: space-evenly;
}

.gap-6 {
  gap: 1.5rem;
}

.gap-8 {
  gap: 2rem;
}

.gap-4 {
  gap: 1rem;
}

.gap-1 {
  gap: 0.25rem;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-0\.5 {
  gap: 0.125rem;
}

.gap-0 {
  gap: 0px;
}

.gap-10 {
  gap: 2.5rem;
}

.gap-1\.5 {
  gap: 0.375rem;
}

.gap-x-2 {
  column-gap: 0.5rem;
}

.gap-x-4 {
  column-gap: 1rem;
}

.gap-x-6 {
  column-gap: 1.5rem;
}

.gap-x-8 {
  column-gap: 2rem;
}

.gap-y-8 {
  row-gap: 2rem;
}

.gap-x-1 {
  column-gap: 0.25rem;
}

.gap-y-2 {
  row-gap: 0.5rem;
}

.gap-y-0 {
  row-gap: 0px;
}

.gap-y-16 {
  row-gap: 4rem;
}

.gap-y-4 {
  row-gap: 1rem;
}

.gap-x-7 {
  column-gap: 1.75rem;
}

.gap-y-1 {
  row-gap: 0.25rem;
}

.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}

.space-x-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1.5rem * var(--tw-space-x-reverse));
  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}

.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}

.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}

.space-x-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(2rem * var(--tw-space-x-reverse));
  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}

.self-end {
  align-self: flex-end;
}

.self-center {
  align-self: center;
}

.self-stretch {
  align-self: stretch;
}

.justify-self-end {
  justify-self: end;
}

.overflow-auto {
  overflow: auto;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-x-auto {
  overflow-x: auto;
}

.overflow-y-auto {
  overflow-y: auto;
}

.overflow-x-hidden {
  overflow-x: hidden;
}

.overflow-y-hidden {
  overflow-y: hidden;
}

.overflow-x-scroll {
  overflow-x: scroll;
}

.overscroll-y-contain {
  overscroll-behavior-y: contain;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.break-words {
  overflow-wrap: break-word;
}

.break-all {
  word-break: break-all;
}

.rounded-full {
  border-radius: 9999px;
}

.rounded-sm {
  border-radius: 0.125rem;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.rounded-md {
  border-radius: 0.375rem;
}

.rounded-xl {
  border-radius: 0.75rem;
}

.rounded {
  border-radius: 0.25rem;
}

.rounded-\[10px\] {
  border-radius: 10px;
}

.rounded-none {
  border-radius: 0px;
}

.rounded-r-full {
  border-top-right-radius: 9999px;
  border-bottom-right-radius: 9999px;
}

.rounded-l-full {
  border-top-left-radius: 9999px;
  border-bottom-left-radius: 9999px;
}

.rounded-l-lg {
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}

.rounded-r-lg {
  border-top-right-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}

.rounded-l-md {
  border-top-left-radius: 0.375rem;
  border-bottom-left-radius: 0.375rem;
}

.rounded-r-md {
  border-top-right-radius: 0.375rem;
  border-bottom-right-radius: 0.375rem;
}

.rounded-l {
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

.rounded-l-none {
  border-top-left-radius: 0px;
  border-bottom-left-radius: 0px;
}

.rounded-r {
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}

.border {
  border-width: 1px;
}

.border-0 {
  border-width: 0px;
}

.border-2 {
  border-width: 2px;
}

.border-t {
  border-top-width: 1px;
}

.border-b {
  border-bottom-width: 1px;
}

.border-b-2 {
  border-bottom-width: 2px;
}

.border-t-2 {
  border-top-width: 2px;
}

.border-l {
  border-left-width: 1px;
}

.border-r {
  border-right-width: 1px;
}

.border-b-0 {
  border-bottom-width: 0px;
}

.border-t-0 {
  border-top-width: 0px;
}

.border-r-0 {
  border-right-width: 0px;
}

.border-l-0 {
  border-left-width: 0px;
}

.border-l-2 {
  border-left-width: 2px;
}

.border-l-4 {
  border-left-width: 4px;
}

.border-solid {
  border-style: solid;
}

.border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity));
}

.border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity));
}

.border-transparent {
  border-color: transparent;
}

.border-gray-100 {
  --tw-border-opacity: 1;
  border-color: rgb(243 244 246 / var(--tw-border-opacity));
}

.border-primary {
  --tw-border-opacity: 1;
  border-color: rgb(66 66 65 / var(--tw-border-opacity));
}

.border-container-darker {
  --tw-border-opacity: 1;
  border-color: rgb(182 182 182 / var(--tw-border-opacity));
}

.border-container {
  --tw-border-opacity: 1;
  border-color: rgb(231 231 231 / var(--tw-border-opacity));
}

.border-secondary {
  --tw-border-opacity: 1;
  border-color: rgb(0 70 126 / var(--tw-border-opacity));
}

.border-primary-lighter {
  --tw-border-opacity: 1;
  border-color: rgb(*********** / var(--tw-border-opacity));
}

.border-gray-lighter {
  --tw-border-opacity: 1;
  border-color: rgb(*********** / var(--tw-border-opacity));
}

.border-black {
  --tw-border-opacity: 1;
  border-color: rgb(0 0 0 / var(--tw-border-opacity));
}

.border-current {
  border-color: currentColor;
}

.border-red-500 {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity));
}

.border-container-lighter {
  --tw-border-opacity: 1;
  border-color: rgb(245 245 245 / var(--tw-border-opacity));
}

.border-blue-500 {
  --tw-border-opacity: 1;
  border-color: rgb(14 165 233 / var(--tw-border-opacity));
}

.border-green-400 {
  --tw-border-opacity: 1;
  border-color: rgb(74 222 128 / var(--tw-border-opacity));
}

.border-yellow-200 {
  --tw-border-opacity: 1;
  border-color: rgb(254 240 138 / var(--tw-border-opacity));
}

.border-blue-200 {
  --tw-border-opacity: 1;
  border-color: rgb(185 223 255 / var(--tw-border-opacity));
}

.border-green-200 {
  --tw-border-opacity: 1;
  border-color: rgb(187 247 208 / var(--tw-border-opacity));
}

.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity));
}

.bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}

.bg-diamond-blue {
  --tw-bg-opacity: 1;
  background-color: rgb(0 70 126 / var(--tw-bg-opacity));
}

.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
}

.bg-gray-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity));
}

.bg-transparent {
  background-color: transparent;
}

.bg-secondary {
  --tw-bg-opacity: 1;
  background-color: rgb(182 37 37 / var(--tw-bg-opacity));
}

.bg-green-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity));
}

.bg-red-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity));
}

.bg-gray-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity));
}

.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity));
}

.bg-red-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity));
}

.bg-gray-400 {
  --tw-bg-opacity: 1;
  background-color: rgb(156 163 175 / var(--tw-bg-opacity));
}

.bg-black\/60 {
  background-color: rgb(0 0 0 / 0.6);
}

.bg-container-darker {
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity));
}

.bg-primary {
  --tw-bg-opacity: 1;
  background-color: rgb(0 70 126 / var(--tw-bg-opacity));
}

.bg-container-lighter {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.bg-white\/60 {
  background-color: rgb(255 255 255 / 0.6);
}

.bg-blue-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(185 223 255 / var(--tw-bg-opacity));
}

.bg-blue-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(9 36 62 / var(--tw-bg-opacity));
}

.bg-shades-darker {
  --tw-bg-opacity: 1;
  background-color: rgb(42 42 41 / var(--tw-bg-opacity));
}

.bg-white\/70 {
  background-color: rgb(255 255 255 / 0.7);
}

.bg-container {
  --tw-bg-opacity: 1;
  background-color: rgb(250 250 250 / var(--tw-bg-opacity));
}

.bg-blue-600 {
  --tw-bg-opacity: 1;
  background-color: rgb(0 70 126 / var(--tw-bg-opacity));
}

.bg-gray-900 {
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity));
}

.bg-white\/25 {
  background-color: rgb(255 255 255 / 0.25);
}

.bg-yellow-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity));
}

.bg-blue-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(240 247 255 / var(--tw-bg-opacity));
}

.bg-red-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 242 242 / var(--tw-bg-opacity));
}

.bg-green-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity));
}

.bg-container-lighter\/95 {
  background-color: rgb(255 255 255 / 0.95);
}

.bg-yellow-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 252 232 / var(--tw-bg-opacity));
}

.bg-green-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity));
}

.bg-opacity-90 {
  --tw-bg-opacity: 0.9;
}

.bg-opacity-40 {
  --tw-bg-opacity: 0.4;
}

.bg-opacity-25 {
  --tw-bg-opacity: 0.25;
}

.bg-opacity-100 {
  --tw-bg-opacity: 1;
}

.bg-opacity-50 {
  --tw-bg-opacity: 0.5;
}

.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.bg-gradient-to-l {
  background-image: linear-gradient(to left, var(--tw-gradient-stops));
}

.from-white {
  --tw-gradient-from: #fff;
  --tw-gradient-to: rgb(255 255 255 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.fill-current {
  fill: currentColor;
}

.fill-black\/20 {
  fill: rgb(0 0 0 / 0.2);
}

.stroke-current {
  stroke: currentColor;
}

.stroke-white\/75 {
  stroke: rgb(255 255 255 / 0.75);
}

.stroke-1 {
  stroke-width: 1;
}

.object-contain {
  object-fit: contain;
}

.object-cover {
  object-fit: cover;
}

.object-center {
  object-position: center;
}

.p-4 {
  padding: 1rem;
}

.p-0 {
  padding: 0px;
}

.p-3 {
  padding: 0.75rem;
}

.p-8 {
  padding: 2rem;
}

.p-6 {
  padding: 1.5rem;
}

.p-2 {
  padding: 0.5rem;
}

.p-1 {
  padding: 0.25rem;
}

.p-2\.5 {
  padding: 0.625rem;
}

.p-10 {
  padding: 2.5rem;
}

.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.py-16 {
  padding-top: 4rem;
  padding-bottom: 4rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.px-1\.5 {
  padding-left: 0.375rem;
  padding-right: 0.375rem;
}

.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}

.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}

.py-0 {
  padding-top: 0px;
  padding-bottom: 0px;
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.px-2\.5 {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.px-0 {
  padding-left: 0px;
  padding-right: 0px;
}

.py-5 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}

.px-10 {
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}

.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}

.px-12 {
  padding-left: 3rem;
  padding-right: 3rem;
}

.px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}

.px-0\.5 {
  padding-left: 0.125rem;
  padding-right: 0.125rem;
}

.pl-1 {
  padding-left: 0.25rem;
}

.pr-1 {
  padding-right: 0.25rem;
}

.pb-6 {
  padding-bottom: 1.5rem;
}

.pr-0 {
  padding-right: 0px;
}

.pb-2 {
  padding-bottom: 0.5rem;
}

.pr-6 {
  padding-right: 1.5rem;
}

.pb-16 {
  padding-bottom: 4rem;
}

.pr-4 {
  padding-right: 1rem;
}

.pb-4 {
  padding-bottom: 1rem;
}

.pt-0 {
  padding-top: 0px;
}

.pb-1 {
  padding-bottom: 0.25rem;
}

.pl-5 {
  padding-left: 1.25rem;
}

.pt-6 {
  padding-top: 1.5rem;
}

.pb-0 {
  padding-bottom: 0px;
}

.pl-4 {
  padding-left: 1rem;
}

.pt-2 {
  padding-top: 0.5rem;
}

.pt-4 {
  padding-top: 1rem;
}

.pt-1 {
  padding-top: 0.25rem;
}

.pb-3 {
  padding-bottom: 0.75rem;
}

.pl-0 {
  padding-left: 0px;
}

.pb-8 {
  padding-bottom: 2rem;
}

.pr-8 {
  padding-right: 2rem;
}

.pl-6 {
  padding-left: 1.5rem;
}

.pt-3 {
  padding-top: 0.75rem;
}

.pl-2 {
  padding-left: 0.5rem;
}

.pt-5 {
  padding-top: 1.25rem;
}

.pb-20 {
  padding-bottom: 5rem;
}

.pr-2 {
  padding-right: 0.5rem;
}

.pl-3 {
  padding-left: 0.75rem;
}

.pr-12 {
  padding-right: 3rem;
}

.pb-12 {
  padding-bottom: 3rem;
}

.pt-16 {
  padding-top: 4rem;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-start {
  text-align: start;
}

.align-top {
  vertical-align: top;
}

.align-middle {
  vertical-align: middle;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.text-6xl {
  font-size: 3.75rem;
  line-height: 1;
}

.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.font-bold {
  font-weight: 700;
}

.font-semibold {
  font-weight: 600;
}

.font-medium {
  font-weight: 500;
}

.font-extralight {
  font-weight: 200;
}

.font-normal {
  font-weight: 400;
}

.font-light {
  font-weight: 300;
}

.uppercase {
  text-transform: uppercase;
}

.lowercase {
  text-transform: lowercase;
}

.italic {
  font-style: italic;
}

.tabular-nums {
  --tw-numeric-spacing: tabular-nums;
  font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);
}

.leading-none {
  line-height: 1;
}

.leading-10 {
  line-height: 2.5rem;
}

.leading-relaxed {
  line-height: 1.625;
}

.leading-tight {
  line-height: 1.25;
}

.leading-6 {
  line-height: 1.5rem;
}

.leading-5 {
  line-height: 1.25rem;
}

.leading-normal {
  line-height: 1.5;
}

.leading-7 {
  line-height: 1.75rem;
}

.tracking-wide {
  letter-spacing: 0.025em;
}

.tracking-widest {
  letter-spacing: 0.1em;
}

.tracking-wider {
  letter-spacing: 0.05em;
}

.text-diamond-blue {
  --tw-text-opacity: 1;
  color: rgb(0 70 126 / var(--tw-text-opacity));
}

.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

.text-primary {
  --tw-text-opacity: 1;
  color: rgb(66 66 65 / var(--tw-text-opacity));
}

.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}

.text-gray-800 {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity));
}

.text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}

.text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity));
}

.text-green-500 {
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity));
}

.text-yellow-500 {
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity));
}

.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.text-green-800 {
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity));
}

.text-red-800 {
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity));
}

.text-quaternary {
  --tw-text-opacity: 1;
  color: rgb(23 93 59 / var(--tw-text-opacity));
}

.text-primary-lighter {
  --tw-text-opacity: 1;
  color: rgb(*********** / var(--tw-text-opacity));
}

.text-stars {
  --tw-text-opacity: 1;
  color: rgb(232 183 35 / var(--tw-text-opacity));
}

.text-stars-empty {
  --tw-text-opacity: 1;
  color: rgb(203 213 224 / var(--tw-text-opacity));
}

.text-secondary {
  --tw-text-opacity: 1;
  color: rgb(0 70 126 / var(--tw-text-opacity));
}

.text-black {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}

.text-gray-300 {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity));
}

.text-secondary-darker {
  --tw-text-opacity: 1;
  color: rgb(2 49 87 / var(--tw-text-opacity));
}

.text-gray-200 {
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity));
}

.text-gray-lighter {
  --tw-text-opacity: 1;
  color: rgb(*********** / var(--tw-text-opacity));
}

.text-primary-darker {
  --tw-text-opacity: 1;
  color: rgb(42 42 41 / var(--tw-text-opacity));
}

.text-blue-200 {
  --tw-text-opacity: 1;
  color: rgb(185 223 255 / var(--tw-text-opacity));
}

.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(*********** / var(--tw-text-opacity));
}

.text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
}

.text-availability-available {
  --tw-text-opacity: 1;
  color: rgb(23 93 59 / var(--tw-text-opacity));
}

.text-red-700 {
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity));
}

.text-yellow-400 {
  --tw-text-opacity: 1;
  color: rgb(250 204 21 / var(--tw-text-opacity));
}

.text-slate-400 {
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity));
}

.text-slate-800 {
  --tw-text-opacity: 1;
  color: rgb(30 41 59 / var(--tw-text-opacity));
}

.text-slate-600 {
  --tw-text-opacity: 1;
  color: rgb(71 85 105 / var(--tw-text-opacity));
}

.text-orange-400 {
  --tw-text-opacity: 1;
  color: rgb(251 146 60 / var(--tw-text-opacity));
}

.text-inherit {
  color: inherit;
}

.text-blue-900 {
  --tw-text-opacity: 1;
  color: rgb(9 36 62 / var(--tw-text-opacity));
}

.text-slate-900 {
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}

.text-yellow-800 {
  --tw-text-opacity: 1;
  color: rgb(133 77 14 / var(--tw-text-opacity));
}

.text-blue-800 {
  --tw-text-opacity: 1;
  color: rgb(3 42 74 / var(--tw-text-opacity));
}

.underline {
  -webkit-text-decoration-line: underline;
          text-decoration-line: underline;
}

.line-through {
  -webkit-text-decoration-line: line-through;
          text-decoration-line: line-through;
}

.\!no-underline {
  -webkit-text-decoration-line: none !important;
          text-decoration-line: none !important;
}

.no-underline {
  -webkit-text-decoration-line: none;
          text-decoration-line: none;
}

.opacity-50 {
  opacity: 0.5;
}

.opacity-70 {
  opacity: 0.7;
}

.opacity-0 {
  opacity: 0;
}

.opacity-100 {
  opacity: 1;
}

.opacity-20 {
  opacity: 0.2;
}

.opacity-60 {
  opacity: 0.6;
}

.opacity-25 {
  opacity: 0.25;
}

.opacity-75 {
  opacity: 0.75;
}

.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-none {
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-xl {
  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.shadow-2xl {
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.outline-offset-2 {
  outline-offset: 2px;
}

.ring {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.ring-primary {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(0 70 126 / var(--tw-ring-opacity));
}

.ring-red-500 {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity));
}

.ring-primary\/50 {
  --tw-ring-color: rgb(0 70 126 / 0.5);
}

.ring-primary\/75 {
  --tw-ring-color: rgb(0 70 126 / 0.75);
}

.ring-blue-500\/50 {
  --tw-ring-color: rgb(14 165 233 / 0.5);
}

.ring-opacity-50 {
  --tw-ring-opacity: 0.5;
}

.blur {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.brightness-0 {
  --tw-brightness: brightness(0);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.invert {
  --tw-invert: invert(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-shadow {
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-colors {
  transition-property: color, background-color, border-color, fill, stroke, -webkit-text-decoration-color;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, -webkit-text-decoration-color;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition {
  transition-property: color, background-color, border-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-text-decoration-color, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-text-decoration-color, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-none {
  transition-property: none;
}

.duration-1000 {
  transition-duration: 1000ms;
}

.duration-200 {
  transition-duration: 200ms;
}

.duration-300 {
  transition-duration: 300ms;
}

.duration-700 {
  transition-duration: 700ms;
}

.duration-500 {
  transition-duration: 500ms;
}

.duration-150 {
  transition-duration: 150ms;
}

.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

.ease-in {
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}

.ease-linear {
  transition-timing-function: linear;
}

.page-main {
  --tw-text-opacity: 1;
  color: rgb(66 66 65 / var(--tw-text-opacity));
}

.table-row-items > div.table-row-item {
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity));
}

.table-row-items > div.table-row-item:nth-child(2n + 1) {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.cms-page-view .column.main a:not([class$="button-primary"]), [data-content-type='row'] a:not([class$="button-primary"]) {
  -webkit-text-decoration-line: underline;
          text-decoration-line: underline;
}

[data-content-type="banner"] > a[data-element='link'] {
  -webkit-text-decoration-line: none;
          text-decoration-line: none;
}

form .field.choice.gdpr-js-content, fieldset .field.choice.gdpr-js-content {
  align-items: start;
}

.category-view .sidebar .block.filter {
}

.account-nav ul li a, .account-nav ul li strong {
  display: flex;
  justify-content: space-between;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  --tw-text-opacity: 1;
  color: rgb(0 70 126 / var(--tw-text-opacity));
}

.account-nav ul li a:hover {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}

.account-nav ul li strong {
  font-weight: 400;
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
  -webkit-text-decoration-line: underline;
          text-decoration-line: underline;
}

.actions-toolbar {
  margin-top: 1.5rem;
  display: flex;
  flex-direction: row-reverse;
  align-items: center;
  justify-content: space-between;
  border-top-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(182 182 182 / var(--tw-border-opacity));
  padding-top: 1rem;
}

.actions-toolbar a.back {
  --tw-text-opacity: 1;
  color: rgb(2 49 87 / var(--tw-text-opacity));
  -webkit-text-decoration-line: underline;
          text-decoration-line: underline;
}

.customer-address-form .page-main .page-title {
  margin-bottom: 1rem;
  width: 100%;
  text-align: center;
}

.customer-address-form .page-main .message {
  margin-top: 1.5rem;
}

.customer-address-form .page-main .message ~ .message {
  margin-top: 0px;
}

.customer-address-form .page-main {
  margin-left: auto;
  margin-right: auto;
  max-width: 1024px;
  border-radius: 0.25rem;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  padding-top: 1rem;
}

body > div > div.grecaptcha-badge {
  display: none;
  height: 0px !important;
  width: 0px !important;
}

.order-items > div:nth-child(even) {
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity));
}

.order-links {
  display: block;
}

[data-content-type$='block'] .order-links p:last-child {
  margin-bottom: 1rem;
  margin-top: 0px;
}

.category-view .sidebar .order-links.filter {
}

.order-links {
  align-items: center;
  --tw-bg-opacity: 1;
  background-color: rgb(250 250 250 / var(--tw-bg-opacity));
}

.order-links li {
  display: inline-block;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 1rem;
  padding-right: 1rem;
  cursor: pointer;
  white-space: nowrap;
}

.order-links li.current {
  flex-grow: 1;
  --tw-text-opacity: 1;
  color: rgb(66 66 65 / var(--tw-text-opacity));
}

.order-links li a {
  -webkit-text-decoration-line: underline;
          text-decoration-line: underline;
}

.order-date {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

form .field, fieldset .field {
  margin-top: 0.25rem;
}

/* Reserve space for single line form validation messages */

form .field.field-reserved, fieldset .field.field-reserved {
  margin-bottom: 1.75rem;
}

form .field.field-reserved ul:last-of-type, fieldset .field.field-reserved ul:last-of-type {
  margin-bottom: -1.5rem;
  padding-bottom: 0.25rem;
}

form .field.field-reserved ul, fieldset .field.field-reserved ul {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

form label, fieldset label {
  margin-bottom: 0.25rem;
  display: block;
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-text-opacity: 1;
  color: rgb(*********** / var(--tw-text-opacity));
}

[data-content-type$='block'] form label p:last-child,[data-content-type$='block']  fieldset label p:last-child {
  margin-bottom: 1rem;
  margin-top: 0px;
}

.category-view .sidebar form label.filter,.category-view .sidebar  fieldset label.filter {
}

form .field.choice, fieldset .field.choice {
  display: flex;
  align-items: center;
}

form .field.choice input, fieldset .field.choice input {
  margin-right: 1rem;
}

form .field.choice label, fieldset .field.choice label {
  margin-bottom: 0px;
}

form .field.field-error .messages, fieldset .field.field-error .messages {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity));
  max-width: -webkit-fit-content;
  max-width: -moz-fit-content;
  max-width: fit-content;
}

form legend, fieldset legend {
  margin-bottom: 0.5rem;
  font-size: 1.25rem;
  line-height: 1.75rem;
  --tw-text-opacity: 1;
  color: rgb(66 66 65 / var(--tw-text-opacity));
}

form legend + br, fieldset legend + br {
  display: none;
}

fieldset ~ fieldset {
  margin-top: 1rem;
}

/* For backwards compatibility */

.flex-columns-wrapper {
  display: flex;
  flex-direction: column;
}

@media (min-width: 768px) {
  .flex-columns-wrapper {
    flex-direction: row;
  }
}

.page-main-full-width .columns {
  max-width: none;
  padding: 0px;
}

/*
 *  Copyright Magmodules.eu. All rights reserved.
 *  See COPYING.txt for license details.
 */

.mollie-buttons {
  margin-top: 10%;
  text-align: center;
  opacity: 0;
  animation: fadeIn 1s;
  animation-delay: 1s;
  animation-fill-mode: forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

.mollie-buttons .button {
  display: inline-block;
  margin: 20px;
  max-width: 100%;
  padding: 0 16px;
  border: 0;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, .19);
  border-radius: 6px;
  background-color: #0095FF;
  color: #FFF;
  font-size: 1.8rem;
  font-weight: 700;
  line-height: 4.4rem;
  text-align: center;
  text-decoration: none;
  outline: none;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: background .15s;
  white-space: nowrap;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.mollie-buttons {
  opacity: 0;
  transition: opacity 2s ease-in;
}

.mollie-buttons .button:hover {
  background-color: #0077CB;
}

.mollie-loading .redirect-block {
  padding: 10%;
  text-align: center;
}

.checkout-payment-method #mollie_methods_ideal-form .label {
  height: 35px;
  display: inline-block;
}

.checkout-payment-method #mollie_methods_ideal-form .payment-icon {
  width: 35px;
  max-height: 30px;
}

.checkout-payment-method #mollie_methods_kbc-form .label {
  height: 35px;
  display: inline-block;
}

.checkout-payment-method #mollie_methods_kbc-form .payment-icon {
  width: 35px;
  max-height: 30px;
}

.checkout-payment-method #mollie_methods_giftcard-form .label {
  height: 40px;
  display: inline-block;
}

.checkout-payment-method #mollie_methods_giftcard-form .payment-icon {
  width: 35px;
  max-height: 30px;
}

.mollie-payment-icon {
  width: 25px;
  margin-right: 5px;
  vertical-align: middle;
}

.mollie-loading .loader {
  border: 16px solid #E3F0FA;
  border-top: 16px solid #31A8F0;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  animation: spin 2s linear infinite;
  margin-left: auto;
  margin-right: auto;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.payment-method-content .card-container {
  max-width: 400px;
  overflow: auto;
  background-color: #FFF;
  border-radius: 10px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  display: block;
  padding: 20px;
  margin-bottom: 10px;
}

/* Apple Pay start */

.apple-pay-button {
  -webkit-appearance: -apple-pay-button;
}

@supports (-webkit-appearance: -apple-pay-button) {
  .apple-pay-button-with-text {
    -webkit-appearance: -apple-pay-button;
    -apple-pay-button-type: buy;
  }

  .apple-pay-button-with-text > * {
    visibility: hidden;
  }

  .apple-pay-button-black-with-text {
    -apple-pay-button-style: black;
  }

  .apple-pay-button-white-with-text {
    -apple-pay-button-style: white;
    cursor: pointer;
  }

  .apple-pay-button-white-with-line-with-text {
    -apple-pay-button-style: white-outline;
  }

  .apple-pay-button-color-black {
    -apple-pay-button-style: black;
  }

  .apple-pay-button-color-white {
    -apple-pay-button-style: white;
  }

  .apple-pay-button-color-white-outline {
    -apple-pay-button-style: white-outline;
  }

  .apple-pay-button-text-buy {
    -apple-pay-button-type: buy;
  }

  .apple-pay-button-text-donate {
    -apple-pay-button-type: donate;
  }

  .apple-pay-button-text-plain {
    -apple-pay-button-type: plain;
  }

  .apple-pay-button-text-book {
    -apple-pay-button-type: book;
  }

  .apple-pay-button-text-check-out {
    -apple-pay-button-type: check-out;
  }

  .apple-pay-button-text-subscribe {
    -apple-pay-button-type: subscribe;
  }

  .apple-pay-button-text-add-money {
    -apple-pay-button-type: add-money;
  }

  .apple-pay-button-text-contribute {
    -apple-pay-button-type: contribute;
  }

  .apple-pay-button-text-order {
    -apple-pay-button-type: order;
  }

  .apple-pay-button-text-reload {
    -apple-pay-button-type: reload;
  }

  .apple-pay-button-text-rent {
    -apple-pay-button-type: rent;
  }

  .apple-pay-button-text-support {
    -apple-pay-button-type: support;
  }

  .apple-pay-button-text-tip {
    -apple-pay-button-type: tip;
  }

  .apple-pay-button-text-top-up {
    -apple-pay-button-type: top-up;
  }
}

.mollie-applepay-button-hidden {
  display: none;
}

.mollie-applepay-minicart-button {
  height: 40px;
}

/* Apple Pay end */

.before\:h-3::before {
  content: var(--tw-content);
  height: 0.75rem;
}

.before\:w-3::before {
  content: var(--tw-content);
  width: 0.75rem;
}

.before\:shrink-0::before {
  content: var(--tw-content);
  flex-shrink: 0;
}

.before\:rounded-full::before {
  content: var(--tw-content);
  border-radius: 9999px;
}

.before\:bg-green-500::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity));
}

.before\:bg-red-500::before {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity));
}

.first\:mt-0:first-child {
  margin-top: 0px;
}

.first\:border-t-0:first-child {
  border-top-width: 0px;
}

.last\:mb-6:last-child {
  margin-bottom: 1.5rem;
}

.last\:mr-0:last-child {
  margin-right: 0px;
}

.last\:mb-0:last-child {
  margin-bottom: 0px;
}

.last\:border-0:last-child {
  border-width: 0px;
}

.last\:border-b:last-child {
  border-bottom-width: 1px;
}

.last\:border-b-0:last-child {
  border-bottom-width: 0px;
}

.last\:pb-0:last-child {
  padding-bottom: 0px;
}

.even\:bg-container-darker:nth-child(even) {
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity));
}

.even\:bg-container:nth-child(even) {
  --tw-bg-opacity: 1;
  background-color: rgb(250 250 250 / var(--tw-bg-opacity));
}

.invalid\:ring-2:invalid {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.invalid\:ring-red-500:invalid {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity));
}

.focus-within\:ring-4:focus-within {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-within\:ring-1:focus-within {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.hover\:border-diamond-blue:hover {
  --tw-border-opacity: 1;
  border-color: rgb(0 70 126 / var(--tw-border-opacity));
}

.hover\:border-primary-lighter:hover {
  --tw-border-opacity: 1;
  border-color: rgb(*********** / var(--tw-border-opacity));
}

.hover\:border-primary:hover {
  --tw-border-opacity: 1;
  border-color: rgb(66 66 65 / var(--tw-border-opacity));
}

.hover\:bg-gray-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity));
}

.hover\:bg-diamond-blue-dark:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(2 49 87 / var(--tw-bg-opacity));
}

.hover\:bg-diamond-blue:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(0 70 126 / var(--tw-bg-opacity));
}

.hover\:bg-white:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.hover\:bg-gray-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}

.hover\:bg-gray-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity));
}

.hover\:bg-container-darker:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity));
}

.hover\:bg-primary\/10:hover {
  background-color: rgb(0 70 126 / 0.1);
}

.hover\:bg-blue-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(240 247 255 / var(--tw-bg-opacity));
}

.hover\:bg-opacity-100:hover {
  --tw-bg-opacity: 1;
}

.hover\:bg-opacity-20:hover {
  --tw-bg-opacity: 0.2;
}

.hover\:pt-0:hover {
  padding-top: 0px;
}

.hover\:pb-1:hover {
  padding-bottom: 0.25rem;
}

.hover\:text-primary:hover {
  --tw-text-opacity: 1;
  color: rgb(66 66 65 / var(--tw-text-opacity));
}

.hover\:text-diamond-blue-dark:hover {
  --tw-text-opacity: 1;
  color: rgb(2 49 87 / var(--tw-text-opacity));
}

.hover\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.hover\:text-gray-300:hover {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity));
}

.hover\:text-secondary:hover {
  --tw-text-opacity: 1;
  color: rgb(0 70 126 / var(--tw-text-opacity));
}

.hover\:text-diamond-blue:hover {
  --tw-text-opacity: 1;
  color: rgb(0 70 126 / var(--tw-text-opacity));
}

.hover\:text-blue-200:hover {
  --tw-text-opacity: 1;
  color: rgb(185 223 255 / var(--tw-text-opacity));
}

.hover\:text-gray-400:hover {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity));
}

.hover\:text-black:hover {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}

.hover\:text-primary-darker:hover {
  --tw-text-opacity: 1;
  color: rgb(42 42 41 / var(--tw-text-opacity));
}

.hover\:text-secondary-darker:hover {
  --tw-text-opacity: 1;
  color: rgb(2 49 87 / var(--tw-text-opacity));
}

.hover\:text-red-600:hover {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity));
}

.hover\:text-tertiary:hover {
  --tw-text-opacity: 1;
  color: rgb(255 224 161 / var(--tw-text-opacity));
}

.hover\:text-slate-800:hover {
  --tw-text-opacity: 1;
  color: rgb(30 41 59 / var(--tw-text-opacity));
}

.hover\:text-gray-500:hover {
  --tw-text-opacity: 1;
  color: rgb(*********** / var(--tw-text-opacity));
}

.hover\:text-yellow-500:hover {
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity));
}

.hover\:text-gray-600:hover {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

.hover\:text-blue-600:hover {
  --tw-text-opacity: 1;
  color: rgb(0 70 126 / var(--tw-text-opacity));
}

.hover\:underline:hover {
  -webkit-text-decoration-line: underline;
          text-decoration-line: underline;
}

.hover\:no-underline:hover {
  -webkit-text-decoration-line: none;
          text-decoration-line: none;
}

.hover\:opacity-100:hover {
  opacity: 1;
}

.hover\:opacity-80:hover {
  opacity: 0.8;
}

.hover\:shadow-lg:hover {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-md:hover {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-2xl:hover {
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-sm:hover {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.focus\:not-sr-only:focus {
  position: static;
  width: auto;
  height: auto;
  padding: 0;
  margin: 0;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

.focus\:absolute:focus {
  position: absolute;
}

.focus\:z-10:focus {
  z-index: 10;
}

.focus\:z-40:focus {
  z-index: 40;
}

.focus\:z-30:focus {
  z-index: 30;
}

.focus\:border-0:focus {
  border-width: 0px;
}

.focus\:border-transparent:focus {
  border-color: transparent;
}

.focus\:border-red-500:focus {
  --tw-border-opacity: 1;
  border-color: rgb(239 68 68 / var(--tw-border-opacity));
}

.focus\:border-blue-300:focus {
  --tw-border-opacity: 1;
  border-color: rgb(124 200 255 / var(--tw-border-opacity));
}

.focus\:border-primary-lighter:focus {
  --tw-border-opacity: 1;
  border-color: rgb(*********** / var(--tw-border-opacity));
}

.focus\:border-primary:focus {
  --tw-border-opacity: 1;
  border-color: rgb(66 66 65 / var(--tw-border-opacity));
}

.focus\:bg-white:focus {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.focus\:text-gray-600:focus {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity));
}

.focus\:no-underline:focus {
  -webkit-text-decoration-line: none;
          text-decoration-line: none;
}

.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-0:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-diamond-blue:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(0 70 126 / var(--tw-ring-opacity));
}

.focus\:ring-red-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity));
}

.focus\:ring-opacity-50:focus {
  --tw-ring-opacity: 0.5;
}

.active\:bg-gray-100:active {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}

.active\:text-gray-500:active {
  --tw-text-opacity: 1;
  color: rgb(*********** / var(--tw-text-opacity));
}

.active\:text-gray-700:active {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity));
}

.active\:ring-0:active {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.disabled\:pointer-events-none:disabled {
  pointer-events: none;
}

.disabled\:bg-gray-100:disabled {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity));
}

.disabled\:opacity-75:disabled {
  opacity: 0.75;
}

.group:hover .group-hover\:rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:border-diamond-blue {
  --tw-border-opacity: 1;
  border-color: rgb(0 70 126 / var(--tw-border-opacity));
}

.group:hover .group-hover\:opacity-100 {
  opacity: 1;
}

.aria-\[current\=page\]\:underline[aria-current=page] {
  -webkit-text-decoration-line: underline;
          text-decoration-line: underline;
}

.group[aria-expanded="true"] .group-aria-expanded\:rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.data-\[active\]\:border-primary[data-active] {
  --tw-border-opacity: 1;
  border-color: rgb(66 66 65 / var(--tw-border-opacity));
}

@media (min-width: 640px) {
  .sm\:static {
    position: static;
  }

  .sm\:z-auto {
    z-index: auto;
  }

  .sm\:order-2 {
    order: 2;
  }

  .sm\:order-1 {
    order: 1;
  }

  .sm\:order-3 {
    order: 3;
  }

  .sm\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .sm\:col-span-6 {
    grid-column: span 6 / span 6;
  }

  .sm\:ml-1 {
    margin-left: 0.25rem;
  }

  .sm\:ml-5 {
    margin-left: 1.25rem;
  }

  .sm\:mb-0 {
    margin-bottom: 0px;
  }

  .sm\:ml-3 {
    margin-left: 0.75rem;
  }

  .sm\:mt-0 {
    margin-top: 0px;
  }

  .sm\:mr-8 {
    margin-right: 2rem;
  }

  .sm\:ml-2 {
    margin-left: 0.5rem;
  }

  .sm\:ml-6 {
    margin-left: 1.5rem;
  }

  .sm\:block {
    display: block;
  }

  .sm\:inline-block {
    display: inline-block;
  }

  .sm\:flex {
    display: flex;
  }

  .sm\:w-1\/2 {
    width: 50%;
  }

  .sm\:w-auto {
    width: auto;
  }

  .sm\:w-1\/6 {
    width: 16.666667%;
  }

  .sm\:w-60 {
    width: 15rem;
  }

  .sm\:w-1\/3 {
    width: 33.333333%;
  }

  .sm\:w-48 {
    width: 12rem;
  }

  .sm\:w-96 {
    width: 24rem;
  }

  .sm\:w-5\/6 {
    width: 83.333333%;
  }

  .sm\:w-20 {
    width: 5rem;
  }

  .sm\:table-fixed {
    table-layout: fixed;
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:grid-cols-8 {
    grid-template-columns: repeat(8, minmax(0, 1fr));
  }

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:flex-nowrap {
    flex-wrap: nowrap;
  }

  .sm\:items-start {
    align-items: flex-start;
  }

  .sm\:gap-8 {
    gap: 2rem;
  }

  .sm\:overflow-hidden {
    overflow: hidden;
  }

  .sm\:border-r-2 {
    border-right-width: 2px;
  }

  .sm\:p-2 {
    padding: 0.5rem;
  }

  .sm\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .sm\:py-0 {
    padding-top: 0px;
    padding-bottom: 0px;
  }

  .sm\:py-24 {
    padding-top: 6rem;
    padding-bottom: 6rem;
  }

  .sm\:py-1 {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
  }

  .sm\:px-3 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }

  .sm\:pb-0 {
    padding-bottom: 0px;
  }

  .sm\:pr-12 {
    padding-right: 3rem;
  }

  .sm\:pl-1 {
    padding-left: 0.25rem;
  }

  .sm\:text-left {
    text-align: left;
  }

  .sm\:text-right {
    text-align: right;
  }

  .sm\:text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .sm\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .sm\:text-5xl {
    font-size: 3rem;
    line-height: 1;
  }

  .sm\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .sm\:duration-700 {
    transition-duration: 700ms;
  }
}

@media (min-width: 768px) {
  .md\:visible {
    visibility: visible;
  }

  .md\:relative {
    position: relative;
  }

  .md\:inset-0 {
    top: 0px;
    right: 0px;
    bottom: 0px;
    left: 0px;
  }

  .md\:z-0 {
    z-index: 0;
  }

  .md\:order-3 {
    order: 3;
  }

  .md\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .md\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .md\:col-start-1 {
    grid-column-start: 1;
  }

  .md\:row-span-2 {
    grid-row: span 2 / span 2;
  }

  .md\:row-start-1 {
    grid-row-start: 1;
  }

  .md\:mx-0 {
    margin-left: 0px;
    margin-right: 0px;
  }

  .md\:mx-auto {
    margin-left: auto;
    margin-right: auto;
  }

  .md\:my-6 {
    margin-top: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .md\:-mx-4 {
    margin-left: -1rem;
    margin-right: -1rem;
  }

  .md\:mx-4 {
    margin-left: 1rem;
    margin-right: 1rem;
  }

  .md\:my-0 {
    margin-top: 0px;
    margin-bottom: 0px;
  }

  .md\:mt-6 {
    margin-top: 1.5rem;
  }

  .md\:-mr-1 {
    margin-right: -0.25rem;
  }

  .md\:mb-0 {
    margin-bottom: 0px;
  }

  .md\:mt-0 {
    margin-top: 0px;
  }

  .md\:mt-2 {
    margin-top: 0.5rem;
  }

  .md\:ml-6 {
    margin-left: 1.5rem;
  }

  .md\:mr-6 {
    margin-right: 1.5rem;
  }

  .md\:mr-16 {
    margin-right: 4rem;
  }

  .md\:mb-6 {
    margin-bottom: 1.5rem;
  }

  .md\:mt-5 {
    margin-top: 1.25rem;
  }

  .md\:mr-5 {
    margin-right: 1.25rem;
  }

  .md\:mt-4 {
    margin-top: 1rem;
  }

  .md\:ml-2 {
    margin-left: 0.5rem;
  }

  .md\:mt-10 {
    margin-top: 2.5rem;
  }

  .md\:mb-3 {
    margin-bottom: 0.75rem;
  }

  .md\:-mt-1 {
    margin-top: -0.25rem;
  }

  .md\:ml-auto {
    margin-left: auto;
  }

  .md\:mr-0 {
    margin-right: 0px;
  }

  .md\:block {
    display: block;
  }

  .md\:inline-block {
    display: inline-block;
  }

  .md\:inline {
    display: inline;
  }

  .md\:flex {
    display: flex;
  }

  .md\:grid {
    display: grid;
  }

  .md\:hidden {
    display: none;
  }

  .md\:h-auto {
    height: auto;
  }

  .md\:h-6 {
    height: 1.5rem;
  }

  .md\:h-24 {
    height: 6rem;
  }

  .md\:w-1\/3 {
    width: 33.333333%;
  }

  .md\:w-2\/3 {
    width: 66.666667%;
  }

  .md\:w-1\/2 {
    width: 50%;
  }

  .md\:w-auto {
    width: auto;
  }

  .md\:w-20 {
    width: 5rem;
  }

  .md\:w-6 {
    width: 1.5rem;
  }

  .md\:w-2\/6 {
    width: 33.333333%;
  }

  .md\:w-40 {
    width: 10rem;
  }

  .md\:w-full {
    width: 100%;
  }

  .md\:w-4\/6 {
    width: 66.666667%;
  }

  .md\:w-24 {
    width: 6rem;
  }

  .md\:shrink-0 {
    flex-shrink: 0;
  }

  .md\:grow {
    flex-grow: 1;
  }

  .md\:-translate-x-1\/3 {
    --tw-translate-x: -33.333333%;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-\[42\%_minmax\(0\2c _1fr\)\] {
    grid-template-columns: 42% minmax(0, 1fr);
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .md\:grid-rows-\[min-content_minmax\(0\2c _1fr\)\] {
    grid-template-rows: -webkit-min-content minmax(0, 1fr);
    grid-template-rows: min-content minmax(0, 1fr);
  }

  .md\:flex-row {
    flex-direction: row;
  }

  .md\:flex-wrap {
    flex-wrap: wrap;
  }

  .md\:items-start {
    align-items: flex-start;
  }

  .md\:items-center {
    align-items: center;
  }

  .md\:justify-start {
    justify-content: flex-start;
  }

  .md\:justify-end {
    justify-content: flex-end;
  }

  .md\:justify-center {
    justify-content: center;
  }

  .md\:justify-between {
    justify-content: space-between;
  }

  .md\:gap-4 {
    gap: 1rem;
  }

  .md\:gap-2 {
    gap: 0.5rem;
  }

  .md\:gap-1 {
    gap: 0.25rem;
  }

  .md\:gap-x-5 {
    column-gap: 1.25rem;
  }

  .md\:gap-x-6 {
    column-gap: 1.5rem;
  }

  .md\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }

  .md\:space-x-6 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(1.5rem * var(--tw-space-x-reverse));
    margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
  }

  .md\:border-0 {
    border-width: 0px;
  }

  .md\:border-l {
    border-left-width: 1px;
  }

  .md\:border-r {
    border-right-width: 1px;
  }

  .md\:border-t {
    border-top-width: 1px;
  }

  .md\:bg-transparent {
    background-color: transparent;
  }

  .md\:p-4 {
    padding: 1rem;
  }

  .md\:px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
  }

  .md\:py-4 {
    padding-top: 1rem;
    padding-bottom: 1rem;
  }

  .md\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .md\:py-6 {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }

  .md\:px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .md\:py-0 {
    padding-top: 0px;
    padding-bottom: 0px;
  }

  .md\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .md\:px-2 {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .md\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .md\:pr-1 {
    padding-right: 0.25rem;
  }

  .md\:pb-0 {
    padding-bottom: 0px;
  }

  .md\:pt-4 {
    padding-top: 1rem;
  }

  .md\:pl-0 {
    padding-left: 0px;
  }

  .md\:pr-8 {
    padding-right: 2rem;
  }

  .md\:pl-5 {
    padding-left: 1.25rem;
  }

  .md\:pl-16 {
    padding-left: 4rem;
  }

  .md\:pt-0 {
    padding-top: 0px;
  }

  .md\:text-left {
    text-align: left;
  }

  .md\:text-right {
    text-align: right;
  }

  .md\:text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .md\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .md\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  .md\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .md\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .md\:opacity-100 {
    opacity: 1;
  }

  .md\:opacity-0 {
    opacity: 0;
  }
}

@media (min-width: 1024px) {
  .lg\:sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
  }

  .lg\:absolute {
    position: absolute;
  }

  .lg\:sticky {
    position: -webkit-sticky;
    position: sticky;
  }

  .lg\:bottom-auto {
    bottom: auto;
  }

  .lg\:right-auto {
    right: auto;
  }

  .lg\:top-\[var\(--msrp-block-offset\)\] {
    top: var(--msrp-block-offset);
  }

  .lg\:left-\[var\(--msrp-inline-offset\)\] {
    left: var(--msrp-inline-offset);
  }

  .lg\:top-2 {
    top: 0.5rem;
  }

  .lg\:order-1 {
    order: 1;
  }

  .lg\:order-2 {
    order: 2;
  }

  .lg\:order-none {
    order: 0;
  }

  .lg\:order-last {
    order: 9999;
  }

  .lg\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .lg\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .lg\:col-span-6 {
    grid-column: span 6 / span 6;
  }

  .lg\:col-start-4 {
    grid-column-start: 4;
  }

  .lg\:row-span-3 {
    grid-row: span 3 / span 3;
  }

  .lg\:row-start-1 {
    grid-row-start: 1;
  }

  .lg\:mt-3 {
    margin-top: 0.75rem;
  }

  .lg\:ml-5 {
    margin-left: 1.25rem;
  }

  .lg\:-mt-6 {
    margin-top: -1.5rem;
  }

  .lg\:mt-8 {
    margin-top: 2rem;
  }

  .lg\:mr-4 {
    margin-right: 1rem;
  }

  .lg\:block {
    display: block;
  }

  .lg\:inline-block {
    display: inline-block;
  }

  .lg\:inline {
    display: inline;
  }

  .lg\:flex {
    display: flex;
  }

  .lg\:table-cell {
    display: table-cell;
  }

  .lg\:table-header-group {
    display: table-header-group;
  }

  .lg\:table-row {
    display: table-row;
  }

  .lg\:grid {
    display: grid;
  }

  .lg\:hidden {
    display: none;
  }

  .lg\:w-1\/4 {
    width: 25%;
  }

  .lg\:w-2\/3 {
    width: 66.666667%;
  }

  .lg\:w-1\/3 {
    width: 33.333333%;
  }

  .lg\:w-1\/2 {
    width: 50%;
  }

  .lg\:w-1\/5 {
    width: 20%;
  }

  .lg\:w-auto {
    width: auto;
  }

  .lg\:w-56 {
    width: 14rem;
  }

  .lg\:w-80 {
    width: 20rem;
  }

  .lg\:max-w-xs {
    max-width: 20rem;
  }

  .lg\:flex-none {
    flex: none;
  }

  .lg\:flex-1 {
    flex: 1 1 0%;
  }

  .lg\:table-auto {
    table-layout: auto;
  }

  .lg\:-translate-y-0 {
    --tw-translate-y: -0px;
    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  }

  .lg\:grid-cols-6 {
    grid-template-columns: repeat(6, minmax(0, 1fr));
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-8 {
    grid-template-columns: repeat(8, minmax(0, 1fr));
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:flex-row {
    flex-direction: row;
  }

  .lg\:flex-row-reverse {
    flex-direction: row-reverse;
  }

  .lg\:flex-col {
    flex-direction: column;
  }

  .lg\:flex-nowrap {
    flex-wrap: nowrap;
  }

  .lg\:items-start {
    align-items: flex-start;
  }

  .lg\:justify-start {
    justify-content: flex-start;
  }

  .lg\:gap-x-8 {
    column-gap: 2rem;
  }

  .lg\:gap-x-10 {
    column-gap: 2.5rem;
  }

  .lg\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-y-reverse: 0;
    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0px * var(--tw-space-y-reverse));
  }

  .lg\:border-0 {
    border-width: 0px;
  }

  .lg\:border-b-0 {
    border-bottom-width: 0px;
  }

  .lg\:border-t {
    border-top-width: 1px;
  }

  .lg\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .lg\:px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .lg\:px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
  }

  .lg\:py-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }

  .lg\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .lg\:py-32 {
    padding-top: 8rem;
    padding-bottom: 8rem;
  }

  .lg\:px-16 {
    padding-left: 4rem;
    padding-right: 4rem;
  }

  .lg\:pr-6 {
    padding-right: 1.5rem;
  }

  .lg\:pl-24 {
    padding-left: 6rem;
  }

  .lg\:pt-2 {
    padding-top: 0.5rem;
  }

  .lg\:text-left {
    text-align: left;
  }

  .lg\:text-right {
    text-align: right;
  }

  .lg\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  .lg\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }

  .lg\:shadow-lg {
    --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }
}

@media (min-width: 1280px) {
  .xl\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .xl\:mt-0 {
    margin-top: 0px;
  }

  .xl\:-mt-12 {
    margin-top: -3rem;
  }

  .xl\:w-1\/5 {
    width: 20%;
  }

  .xl\:w-1\/2 {
    width: 50%;
  }

  .xl\:w-1\/4 {
    width: 25%;
  }

  .xl\:w-1\/3 {
    width: 33.333333%;
  }

  .xl\:grow {
    flex-grow: 1;
  }

  .xl\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .xl\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .xl\:border-none {
    border-style: none;
  }
}

@media (min-width: 1770px) {
  .\32xl\:w-96 {
    width: 24rem;
  }
}
